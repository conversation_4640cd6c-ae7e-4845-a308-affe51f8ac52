# Image Processing Agent Testing Guide

This guide provides step-by-step instructions for testing the Image Processing Agent system using both the A2A server and client.

## System Architecture

```
[Postman/HTTP Client] → [A2A Client (Port 8006)] → [A2A Image Processing Agent (Port 8005)]
                              ↓ A2A SDK
                        [A2AClient + httpx]
```

- **A2A Image Processing Agent (Port 8005)**: The main agent that processes images using Google Gemini vision
- **A2A Client (Port 8006)**: HTTP bridge that accepts file uploads and converts them to A2A protocol using the official A2A SDK

## Prerequisites

1. **Python 3.8+** installed
2. **Dependencies installed**: `pip install -r requirements.txt`
3. **Google API Key** configured in `.env` file
4. **Postman** or similar HTTP client for testing

## Quick Start

### Step 1: Start the Image Processing Agent Server

```bash
cd Image_Processing_Agent
python start_image_processing_server.py
```

**Expected Output:**
```
✅ Prerequisites check passed
🚀 Starting Image Processing Agent server...
✅ Server is healthy and responding at http://localhost:8005
🎉 Image Processing Agent server is running successfully!
🌐 Agent card: http://localhost:8005/.well-known/agent.json
🔗 A2A endpoint: http://localhost:8005/a2a/image_processing_agent
🖼️  Ready to process images with Google Gemini vision!
```

### Step 2: Start the A2A Client Server

Open a **new terminal** and run:

```bash
cd Image_Processing_Agent
python start_image_client.py
```

**Expected Output:**
```
✅ Image Processing Agent is running at http://localhost:8005
✅ Prerequisites check passed
🚀 Starting A2A Image Processing Client...
A2A client connected successfully during startup
✅ Client server is healthy and responding at http://localhost:8006
🎉 A2A Image Processing Client is running successfully!
🌐 Client URL: http://localhost:8006
📋 Upload endpoint: http://localhost:8006/upload
📖 API docs: http://localhost:8006/docs
Using A2A SDK for agent communication
```

## Testing with Postman

### Basic File Upload Test

1. **Open Postman** and create a new request

2. **Set Request Method**: `POST`

3. **Set URL**: `http://localhost:8006/upload`

4. **Configure Body**:
   - Go to the **Body** tab
   - Select **form-data**
   - Add a new key with the following settings:
     - **Key**: `files`
     - **Type**: `File` (select from dropdown)
     - **Value**: Click "Select Files" and choose one or more image files

5. **Optional Parameters** (add as text fields):
   - **Key**: `user_id`, **Type**: `Text`, **Value**: `test_user_123`
   - **Key**: `description`, **Type**: `Text`, **Value**: `Test image analysis`

6. **Send the Request**

### Expected Response Format

```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "context_id": "550e8400-e29b-41d4-a716-446655440001",
  "user_id": "test_user_123",
  "timestamp": 1704067200000,
  "uploaded_files": 2,
  "files_info": [
    {
      "id": "img-001",
      "name": "document",
      "original_filename": "document.png",
      "size": 245760,
      "mime_type": "image/png"
    },
    {
      "id": "img-002",
      "name": "photo",
      "original_filename": "photo.jpg",
      "size": 156432,
      "mime_type": "image/jpeg"
    }
  ],
  "processing_result": {
    "status": "success",
    "response_type": "json",
    "processed_images": 2,
    "results": [
      {
        "id": "img-001",
        "original_name": "document",
        "updated_name": "Financial_Report_Q3_2024",
        "content_description": "A financial report document with tables and charts",
        "extracted_text": "Q3 2024 Financial Report...",
        "visual_elements": {
          "objects": ["table", "chart", "logo"],
          "layout": "Professional document layout",
          "colors": ["blue", "white", "gray"],
          "style": "Corporate financial document"
        },
        "context_analysis": "Quarterly financial report",
        "quality_assessment": {
          "clarity": "High quality, clear text",
          "resolution": "Good resolution for text extraction",
          "issues": "None detected"
        },
        "suggested_tags": ["financial", "report", "quarterly"],
        "confidence_score": 0.95
      },
      {
        "id": "img-002",
        "original_name": "photo",
        "updated_name": "Office_Meeting_Photo",
        "content_description": "A photograph of people in a business meeting",
        "extracted_text": "Meeting Room A - Q3 Review",
        "visual_elements": {
          "objects": ["people", "table", "laptops", "presentation screen"],
          "layout": "Conference room setting with people around table",
          "colors": ["blue", "white", "brown"],
          "style": "Business photography"
        },
        "context_analysis": "Business meeting or conference",
        "quality_assessment": {
          "clarity": "Good lighting and focus",
          "resolution": "High resolution image",
          "issues": "None detected"
        },
        "suggested_tags": ["business", "meeting", "office", "teamwork"],
        "confidence_score": 0.92
      }
    ]
  },
  "individual_results": [
    {
      "id": "img-001",
      "original_name": "document",
      "updated_name": "Financial_Report_Q3_2024",
      "content_description": "A financial report document with tables and charts",
      "extracted_text": "Q3 2024 Financial Report...",
      "visual_elements": {
        "objects": ["table", "chart", "logo"],
        "layout": "Professional document layout",
        "colors": ["blue", "white", "gray"],
        "style": "Corporate financial document"
      },
      "context_analysis": "Quarterly financial report",
      "quality_assessment": {
        "clarity": "High quality, clear text",
        "resolution": "Good resolution for text extraction",
        "issues": "None detected"
      },
      "suggested_tags": ["financial", "report", "quarterly"],
      "confidence_score": 0.95
    },
    {
      "id": "img-002",
      "original_name": "photo",
      "updated_name": "Office_Meeting_Photo",
      "content_description": "A photograph of people in a business meeting",
      "extracted_text": "Meeting Room A - Q3 Review",
      "visual_elements": {
        "objects": ["people", "table", "laptops", "presentation screen"],
        "layout": "Conference room setting with people around table",
        "colors": ["blue", "white", "brown"],
        "style": "Business photography"
      },
      "context_analysis": "Business meeting or conference",
      "quality_assessment": {
        "clarity": "Good lighting and focus",
        "resolution": "High resolution image",
        "issues": "None detected"
      },
      "suggested_tags": ["business", "meeting", "office", "teamwork"],
      "confidence_score": 0.92
    }
  ],
  "status": "completed"
}
```

## Advanced Testing Scenarios

### Multiple File Upload

1. In Postman, add multiple files to the same `files` key
2. You can select multiple files at once or add multiple `files` entries
3. The system will process all images and return analysis for each

### Different Image Formats

Test with various supported formats:
- PNG files
- JPEG/JPG files  
- GIF files
- WebP files
- BMP files
- TIFF files

### Large File Testing

- Test with high-resolution images (up to 20MB)
- Monitor processing time and response quality

## API Documentation

### Interactive Documentation

Visit `http://localhost:8006/docs` for interactive Swagger UI documentation.

### Health Check Endpoints

- **Client Health**: `GET http://localhost:8006/health`
- **Agent Health**: `GET http://localhost:8005/.well-known/agent.json`

## Troubleshooting

### Common Issues

1. **"Connection refused" errors**
   - Ensure both servers are running
   - Check that ports 8005 and 8006 are not in use by other applications

2. **"Google API key not found" errors**
   - Verify the `.env` file contains the correct `GOOGLE_API_KEY`
   - Check that the API key is valid and has Gemini API access

3. **"Unsupported file format" errors**
   - Ensure you're uploading supported image formats
   - Check file extensions are correct

4. **Timeout errors**
   - Large images may take longer to process
   - Check network connectivity between client and agent

### Logs and Debugging

- **Agent Logs**: Check the terminal running `start_image_processing_server.py`
- **Client Logs**: Check the terminal running `start_image_client.py`
- **Detailed Logs**: Both servers provide detailed logging for debugging

## Performance Notes

- **Processing Time**: Varies based on image size and complexity (typically 5-30 seconds)
- **Concurrent Requests**: The system can handle multiple simultaneous requests
- **Memory Usage**: Large images will use more memory during processing

## Integration Examples

### cURL Example

```bash
curl -X POST "http://localhost:8006/upload" \
  -F "files=@/path/to/image1.png" \
  -F "files=@/path/to/image2.jpg" \
  -F "user_id=test_user" \
  -F "description=Test batch processing"
```

### Python Requests Example

```python
import requests

url = "http://localhost:8006/upload"
files = [
    ('files', ('image1.png', open('image1.png', 'rb'), 'image/png')),
    ('files', ('image2.jpg', open('image2.jpg', 'rb'), 'image/jpeg'))
]
data = {
    'user_id': 'test_user',
    'description': 'Python API test'
}

response = requests.post(url, files=files, data=data)
result = response.json()
print(result)
```

## Next Steps

1. **Production Deployment**: Configure proper authentication and rate limiting
2. **Monitoring**: Add metrics and monitoring for production use
3. **Scaling**: Consider load balancing for high-volume scenarios
4. **Integration**: Connect to your existing workflows and systems

For additional support, check the logs and ensure all prerequisites are met.
