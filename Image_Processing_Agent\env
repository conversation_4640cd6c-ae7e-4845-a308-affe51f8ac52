# Google Gemini API Configuration
GOOGLE_API_KEY=AIzaSyB5jvOM9xD7fvDs8C5b4T2N-u58chACTo0
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Server Configuration
A2A_IMAGE_HOST=localhost
A2A_IMAGE_PORT=8005

# Agent Configuration
AGENT_NAME=Image Processing Agent
AGENT_VERSION=1.0.0

# Logging Configuration
LOG_LEVEL=INFO

# Image Processing Configuration
MAX_IMAGE_SIZE=20971520  # 20MB in bytes
MAX_IMAGES_PER_REQUEST=10
SUPPORTED_IMAGE_FORMATS=png,jpg,jpeg,gif,webp,bmp,tiff

# Gemini Model Configuration
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_OUTPUT_TOKENS=4096

#OPEN AI Configuration
OPENAI_MODEL=gpt-4o-mini
