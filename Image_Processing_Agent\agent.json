{"name": "Image Processing Agent", "description": "Advanced image processing agent powered by Google Gemini vision capabilities. Analyzes images to extract text, identify objects, understand content, and provide comprehensive structured responses.", "version": "1.0.0", "url": "http://localhost:8005/a2a/image_processing_agent", "defaultInputModes": ["multipart/form-data", "application/json", "text/plain"], "defaultOutputModes": ["application/json", "text/plain"], "capabilities": {"streaming": false, "pushNotifications": false, "fileUpload": true, "multimodal": true, "vision": true}, "skills": [{"id": "image_analysis", "name": "Image Analysis", "description": "Analyze images using Google Gemini vision capabilities to extract text, identify objects, and provide comprehensive content analysis.", "tags": ["vision", "image", "analysis", "ocr", "gemini"], "examples": ["Analyze this screenshot and extract all visible text", "Process these document images and provide detailed content analysis", "Identify objects and text in these photos"]}, {"id": "content_extraction", "name": "Content Extraction", "description": "Extract text content from images using advanced OCR capabilities and provide structured metadata.", "tags": ["ocr", "text-extraction", "content", "metadata"], "examples": ["Extract all text from these document images", "OCR processing for batch image analysis", "Convert image text to structured data"]}, {"id": "visual_understanding", "name": "Visual Understanding", "description": "Understand visual content including objects, scenes, layouts, and contextual information in images.", "tags": ["vision", "understanding", "objects", "scenes", "context"], "examples": ["Describe what you see in these images", "Identify objects and their relationships in photos", "Analyze the layout and design of these screenshots"]}], "supportedFormats": {"input": {"images": ["PNG", "JPEG", "JPG", "GIF", "WebP", "BMP", "TIFF"], "data": ["base64", "multipart/form-data", "file-upload"]}, "output": {"structured": ["JSON"], "text": ["plain-text", "markdown"]}}, "technicalDetails": {"model": "gemini-2.0-flash-exp", "provider": "Google", "capabilities": ["Vision processing", "OCR (Optical Character Recognition)", "Object detection and identification", "Scene understanding", "Text extraction and analysis", "Content categorization", "Metadata generation"], "limits": {"maxImageSize": "20MB", "maxImagesPerRequest": 10, "supportedMimeTypes": ["image/jpeg", "image/png", "image/gif", "image/webp", "image/bmp", "image/tiff"]}}, "endpoints": {"agentCard": "/.well-known/agent.json", "rpc": "/a2a/image_processing_agent", "extendedCard": "/agent/authenticatedExtendedCard"}, "protocol": {"type": "A2A", "version": "1.0", "transport": "HTTP/JSON-RPC"}}