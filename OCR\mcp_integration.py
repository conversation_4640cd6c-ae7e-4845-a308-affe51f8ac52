# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Firecrawl MCP integration for the OCR Web Search Agent."""

import logging
import os
from typing import Optional

from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
from mcp import StdioServerParameters

from config import config

logger = logging.getLogger(__name__)


def create_firecrawl_mcp_toolset() -> MCPToolset:
    """Create and configure the Firecrawl MCP toolset.
    
    Returns:
        MCPToolset: Configured Firecrawl MCP toolset with proper environment setup.
    """
    # Set up environment variables for the MCP server
    env = os.environ.copy()
    env["FIRECRAWL_API_KEY"] = config.FIRECRAWL_API_KEY
    
    # Create the MCP server parameters for Firecrawl
    server_params = StdioServerParameters(
        command=config.MCP_COMMAND,
        args=config.MCP_ARGS,
        env=env
    )
    
    # Create the MCPToolset with Firecrawl configuration
    toolset = MCPToolset(
        connection_params=server_params,
        tool_filter=None,  # Allow all Firecrawl tools
    )
    
    logger.info(
        f"Created Firecrawl MCP toolset with command: {config.MCP_COMMAND} {' '.join(config.MCP_ARGS)}"
    )
    
    return toolset


def get_firecrawl_tools_description() -> str:
    """Get a description of available Firecrawl tools for the agent instruction.
    
    Returns:
        str: Description of Firecrawl capabilities for agent instructions.
    """
    return """
    You have access to Firecrawl MCP tools that provide web search and content extraction capabilities:
    
    - Web crawling: Extract content from web pages
    - Search functionality: Perform web searches
    - Content processing: Clean and structure web content
    - URL handling: Process and validate URLs
    
    Use these tools to help users with web search queries, content extraction from websites,
    and gathering information from the internet. Always provide clear and helpful responses
    based on the information you retrieve.
    """
