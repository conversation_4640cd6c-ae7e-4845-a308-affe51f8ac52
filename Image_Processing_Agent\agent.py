"""
Image Processing Agent using Google Gemini 2.0 Flash model with vision capabilities.
"""

import os
from google.adk.agents.llm_agent import Agent
from google.adk.models.google_llm import Gemini

from prompt import IMAGE_PROCESSING_PROMPT


def create_image_processing_agent() -> Agent:
    """
    Create an Image Processing Agent using Google Gemini 2.0 Flash model.
    
    This agent is configured with:
    - Gemini 2.0 Flash model for multimodal capabilities
    - Vision processing for image analysis
    - Structured response generation
    - Google Gemini API key authentication
    
    Returns:
        Agent: Configured image processing agent
    """
    
    # Google Gemini API key
    api_key = "AIzaSyB5jvOM9xD7fvDs8C5b4T2N-u58chACTo0"
    
    # Set the API key in environment if not already set
    if not os.getenv("GOOGLE_API_KEY"):
        os.environ["GOOGLE_API_KEY"] = api_key
    
    # Create Gemini model with vision capabilities
    # gemini_model = Gemini(
    #     model_name="gemini-2.0-flash-exp",  # Latest Gemini model with vision
    #     api_key=api_key,
    #     temperature=0.1,  # Low temperature for consistent, factual analysis
    #     max_output_tokens=4096,  # Sufficient for detailed structured responses
    # )
    
    # Create the agent with image processing capabilities
    agent = Agent(
        name="image_processing_agent",
        model="gemini-2.0-flash-exp",
        description="Advanced image processing agent that analyzes visual content using Google Gemini's vision capabilities and provides comprehensive structured responses.",
        instruction=IMAGE_PROCESSING_PROMPT,
        tools=[],  # No additional tools needed - Gemini handles vision natively
    )
    
    return agent


# Create the root agent instance
root_agent = create_image_processing_agent()
