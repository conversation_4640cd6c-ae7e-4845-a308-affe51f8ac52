"""
Enhanced Image Processing Agent Executor with Cursor Tracking and Memory Integration.
Extends the base agent executor with video frame analysis and cursor tracking capabilities.
"""

import logging
import uuid
import json
import base64
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path

from agent_executor import ImageProcessingExecutor
from cursor_tracking_service import CursorTrackingService, CursorPosition
from a2a.server.agent_execution import RequestContext
from a2a.server.events import EventQueue
from a2a.types import AgentCard
from google.adk.agents import Agent
from google.adk.runners import Runner

logger = logging.getLogger(__name__)


class EnhancedImageProcessingExecutor(ImageProcessingExecutor):
    """
    Enhanced executor with cursor tracking and video frame analysis capabilities.
    """
    
    def __init__(self, agent: Agent, agent_card: AgentCard, runner: Runner):
        super().__init__(agent, agent_card, runner)
        self.cursor_tracking_service: Optional[CursorTrackingService] = None
        
    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        """
        Execute enhanced image processing with cursor tracking for video frames.
        """
        try:
            # Initialize cursor tracking service for this session
            session_id = context.task_id or str(uuid.uuid4())
            self.cursor_tracking_service = CursorTrackingService(
                memory_service=self.runner.memory_service,
                session_id=session_id
            )
            
            # Step 1: Prepare image input for processing
            processed_input = await self._prepare_image_input(context)
            
            # Step 2: Check if this is video frame data
            is_video_frames = self._is_video_frame_data(processed_input)
            
            # Step 3: Process cursor tracking for video frames
            if is_video_frames:
                await self._process_cursor_tracking(processed_input)
            
            # Step 4: Set up session identifiers for A2A protocol
            user_id, session_id = self._generate_session_identifiers(context)
            await self._ensure_adk_session(user_id, session_id)
            
            # Step 5: Process images with enhanced analysis
            analysis_result = await self._process_images_with_enhanced_analysis(
                processed_input, user_id, session_id, is_video_frames
            )
            
            # Step 6: Add cursor movement analysis if applicable
            if is_video_frames and self.cursor_tracking_service:
                cursor_analysis = await self.cursor_tracking_service.get_cursor_movement_analysis()
                analysis_result = await self._merge_cursor_analysis(analysis_result, cursor_analysis)
            
            # Step 7: Send enhanced response back via A2A protocol
            await self._send_response(event_queue, context, analysis_result)
            
        except Exception as e:
            await self._handle_error(event_queue, context, e)
    
    def _is_video_frame_data(self, processed_input: Dict[str, Any]) -> bool:
        """Check if the input contains video frame data."""
        images = processed_input.get("images", [])
        if not images:
            return False
        
        # Check for video frame indicators
        for image in images:
            if (image.get("type") == "video_frame" or 
                "frame_number" in image or 
                "video_timestamp" in image or
                "source_video" in image):
                return True
        
        return False
    
    async def _process_cursor_tracking(self, processed_input: Dict[str, Any]):
        """Process cursor tracking for video frames."""
        if not self.cursor_tracking_service:
            return
        
        images = processed_input.get("images", [])
        
        for image_info in images:
            if image_info.get("type") == "video_frame":
                frame_number = image_info.get("frame_number", 0)
                timestamp = image_info.get("video_timestamp", 0.0)
                base64_data = image_info.get("data")
                
                if base64_data:
                    # Remove data URL prefix if present
                    if base64_data.startswith("data:image/"):
                        base64_data = base64_data.split(",", 1)[1]
                    
                    # Detect cursor in this frame
                    cursor_pos = await self.cursor_tracking_service.detect_cursor_in_frame(
                        base64_data, frame_number, timestamp
                    )
                    
                    if cursor_pos:
                        logger.info(f"Detected cursor at ({cursor_pos.x}, {cursor_pos.y}) in frame {frame_number}")
                        # Store cursor position in image metadata for later use
                        image_info["detected_cursor"] = {
                            "x": cursor_pos.x,
                            "y": cursor_pos.y,
                            "type": cursor_pos.cursor_type,
                            "confidence": cursor_pos.confidence
                        }
    
    async def _process_images_with_enhanced_analysis(
        self, 
        processed_input: Dict[str, Any], 
        user_id: str, 
        session_id: str,
        is_video_frames: bool
    ) -> str:
        """Process images with enhanced analysis including cursor and URL detection."""
        
        images = processed_input["images"]
        message = processed_input["message"]
        
        if not images:
            return json.dumps({
                "status": "success",
                "processed_images": 0,
                "message": "No images provided for processing",
                "results": []
            }, indent=2)

        # Enhanced instruction for video frames
        if is_video_frames:
            enhanced_message = f"""
            {message}
            
            SPECIAL INSTRUCTIONS FOR VIDEO FRAME ANALYSIS:
            
            1. MOUSE CURSOR DETECTION: Look carefully for mouse cursors in each frame. Provide exact pixel coordinates (x, y) where you see the cursor. Describe the cursor type (arrow, hand, text, loading, etc.).
            
            2. BROWSER URL EXTRACTION: If this is a browser screenshot, extract the complete URL from the address bar. Include the full URL, domain, protocol (http/https), and any visible page title.
            
            3. UI ELEMENT TRACKING: Identify clickable elements like buttons, links, form fields. Provide their approximate coordinates and current state (normal, hover, focus, etc.).
            
            4. SCREEN ACTIVITY: Note any loading indicators, animations, form interactions, or notifications visible in the frame.
            
            5. FRAME CONTEXT: For video frames, consider the temporal context - what action might be happening between frames.
            
            Please analyze each frame thoroughly and provide the enhanced JSON structure with mouse_cursor, browser_info, ui_elements, and screen_activity fields.
            """
        else:
            enhanced_message = message

        # Create instruction for the vision model
        instruction = self._create_enhanced_instruction(images, enhanced_message, is_video_frames)
        
        # Process with the vision model (same as parent class but with enhanced instruction)
        return await self._process_images_with_gemini(
            {"images": images, "message": enhanced_message}, 
            user_id, 
            session_id
        )
    
    def _create_enhanced_instruction(self, images: List[Dict[str, Any]], message: str, is_video_frames: bool) -> str:
        """Create enhanced instruction for video frame analysis."""
        instruction = f"""
        {message}

        I have {len(images)} image(s) to analyze. 
        """
        
        if is_video_frames:
            instruction += """
            These are VIDEO FRAMES from a screen recording. Please pay special attention to:
            - Mouse cursor positions and movements
            - Browser URLs and navigation
            - UI interactions and element states
            - Screen activities and transitions
            """
        
        instruction += """
        Please provide comprehensive analysis following the specified JSON format with enhanced fields for cursor tracking and UI analysis.

        Image details:
        """
        
        for i, img in enumerate(images):
            instruction += f"""
        Image {i+1}:
        - ID: {img['id']}
        - Original Name: {img['original_name']}
        - Description: {img.get('description', 'Image for processing')}
        """
            
            # Add video frame specific details
            if img.get("type") == "video_frame":
                instruction += f"""
        - Frame Number: {img.get('frame_number', 'Unknown')}
        - Video Timestamp: {img.get('video_timestamp', 'Unknown')}s
        - Source Video: {img.get('source_video', 'Unknown')}
        """
                
                # Add detected cursor info if available
                if "detected_cursor" in img:
                    cursor_info = img["detected_cursor"]
                    instruction += f"""
        - Detected Cursor: ({cursor_info['x']}, {cursor_info['y']}) - {cursor_info['type']} (confidence: {cursor_info['confidence']:.2f})
        """

        instruction += """
        
        Please analyze all images and return a valid JSON response with the enhanced structure including cursor tracking and UI analysis.
        """
        
        return instruction
    
    async def _merge_cursor_analysis(self, analysis_result: str, cursor_analysis: Dict[str, Any]) -> str:
        """Merge cursor movement analysis with image analysis results."""
        try:
            # Parse the existing analysis result
            result_data = json.loads(analysis_result)
            
            # Add cursor movement analysis
            result_data["cursor_movement_analysis"] = cursor_analysis
            
            # Add summary of cursor tracking
            if cursor_analysis.get("status") == "success":
                result_data["cursor_tracking_summary"] = {
                    "total_positions_tracked": cursor_analysis.get("total_positions", 0),
                    "total_movement_distance": cursor_analysis.get("total_distance", 0),
                    "average_velocity": cursor_analysis.get("average_velocity", 0),
                    "cursor_types_detected": cursor_analysis.get("cursor_types_detected", []),
                    "tracking_duration": cursor_analysis.get("time_span", {}).get("duration", 0)
                }
            
            return json.dumps(result_data, indent=2)
            
        except Exception as e:
            logger.error(f"Error merging cursor analysis: {e}")
            # Return original result if merging fails
            return analysis_result
