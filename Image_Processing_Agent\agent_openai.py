"""
Image Processing Agent using OpenAI GPT-4o-mini model with vision capabilities.
Alternative to the Gemini-based agent for video frame analysis and cursor tracking.
"""

import os
from google.adk.agents.llm_agent import Agent
from google.adk.models.openai import OpenAI

from prompt import IMAGE_PROCESSING_PROMPT


def create_openai_image_processing_agent() -> Agent:
    """
    Create an Image Processing Agent using OpenAI GPT-4o-mini model.
    
    This agent is configured with:
    - GPT-4o-mini model for multimodal capabilities
    - Vision processing for image analysis
    - Enhanced cursor tracking and URL extraction
    - Structured response generation
    - OpenAI API key authentication
    
    Returns:
        Agent: Configured image processing agent
    """
    
    # OpenAI API key - you'll need to set this
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OPENAI_API_KEY environment variable is required")
    
    # Create OpenAI model with vision capabilities
    openai_model = OpenAI(
        model_name="gpt-4o-mini",  # GPT-4o-mini with vision
        api_key=api_key,
        temperature=0.1,  # Low temperature for consistent, factual analysis
        max_tokens=4096,  # Sufficient for detailed structured responses
    )
    
    # Create the agent with image processing capabilities
    agent = Agent(
        name="openai_image_processing_agent",
        model=openai_model,
        description="Advanced image processing agent that analyzes visual content using OpenAI GPT-4o-mini vision capabilities, with specialized support for video frame analysis, cursor tracking, and URL extraction.",
        instruction=IMAGE_PROCESSING_PROMPT,
        tools=[],  # No additional tools needed - GPT-4o-mini handles vision natively
    )
    
    return agent


# Create the root agent instance
root_agent = create_openai_image_processing_agent()
