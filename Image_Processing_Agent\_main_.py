"""
Main entry point for the Image Processing Agent A2A server.
Sets up the A2A server with FastAPI/Starlette, configures routes, and handles server startup.
"""

import logging
import os
import sys
from pathlib import Path

# Add parent directory to Python path for imports
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

import click
import uvicorn

# A2A server imports
from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import <PERSON><PERSON>ultR<PERSON><PERSON><PERSON><PERSON>ler
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import AgentCapabilities, AgentCard, AgentSkill
from dotenv import load_dotenv

# ADK imports
from google.adk.artifacts import InMemoryArtifactService
from google.adk.memory.in_memory_memory_service import InMemoryMemoryService
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService

# try:
#     # Try absolute imports first (when run from parent directory)
#     from Image_Processing_Agent.agent_executor import ImageProcessingExecutor
#     from Image_Processing_Agent.agent import create_image_processing_agent
# except ImportError:
    # Fall back to relative imports (when run from Image_Processing_Agent directory)
from agent_executor import ImageProcessingExecutor
from agent import create_image_processing_agent

# Load environment variables
load_dotenv()

# Basic logging configuration
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--host",
    "host",
    default=os.getenv("A2A_IMAGE_HOST", "localhost"),
    show_default=True,
    help="Host for the Image Processing Agent server."
)
@click.option(
    "--port",
    "port",
    default=int(os.getenv("A2A_IMAGE_PORT", 8005)),  # Default port for Image Processing Agent
    show_default=True,
    type=int,
    help="Port for the Image Processing Agent server."
)
def main(host: str, port: int) -> None:
    """
    Start the Image Processing Agent A2A server.
    
    This server provides image analysis capabilities using Google Gemini's vision model.
    It accepts multipart/form-data file uploads and returns structured analysis results.
    """
    
    logger.info("Initializing Image Processing Agent with Google Gemini vision capabilities")
    
    # Verify API key is available
    api_key = os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")
    if not api_key:
        logger.error("OPENAI API key not found. Please set OPENAI_API_KEY environment variable.")
        sys.exit(1)
    
    # Define agent skills
    image_analysis_skill = AgentSkill(
        id="image_analysis",
        name="Image Analysis",
        description="Analyze images using Google Gemini vision capabilities to extract text, identify objects, and provide comprehensive content analysis.",
        tags=["vision", "image", "analysis", "ocr", "gemini"],
        examples=[
            "Analyze this screenshot and extract all visible text",
            "Process these document images and provide detailed content analysis",
            "Identify objects and text in these photos"
        ],
    )
    
    content_extraction_skill = AgentSkill(
        id="content_extraction",
        name="Content Extraction",
        description="Extract text content from images using advanced OCR capabilities and provide structured metadata.",
        tags=["ocr", "text-extraction", "content", "metadata"],
        examples=[
            "Extract all text from these document images",
            "OCR processing for batch image analysis",
            "Convert image text to structured data"
        ],
    )
    
    visual_understanding_skill = AgentSkill(
        id="visual_understanding",
        name="Visual Understanding",
        description="Understand visual content including objects, scenes, layouts, and contextual information in images.",
        tags=["vision", "understanding", "objects", "scenes", "context"],
        examples=[
            "Describe what you see in these images",
            "Identify objects and their relationships in photos",
            "Analyze the layout and design of these screenshots"
        ],
    )

    # Create agent card
    agent_card = AgentCard(
        name="Image Processing Agent",
        description="Advanced image processing agent powered by Google Gemini vision capabilities. Analyzes images to extract text, identify objects, understand content, and provide comprehensive structured responses.",
        url=f"http://{host}:{port}/",  # URL is dynamically set here
        version="1.0.0",
        defaultInputModes=["multipart/form-data", "application/json", "text/plain"],  # Supports file uploads and JSON
        defaultOutputModes=["application/json", "text/plain"],  # Returns structured JSON responses
        capabilities=AgentCapabilities(
            streaming=False,  # Image processing is batch-based
            pushNotifications=False  # No push notifications needed
        ),
        skills=[image_analysis_skill, content_extraction_skill, visual_understanding_skill]
    )

    # Create the agent
    agent = create_image_processing_agent()

    # Create ADK runner with required services
    runner = Runner(
        agent=agent,
        app_name=agent_card.name,
        artifact_service=InMemoryArtifactService(),
        session_service=InMemorySessionService(),
        memory_service=InMemoryMemoryService(),
    )

    # Create agent executor
    agent_executor = ImageProcessingExecutor(
        agent=agent,
        agent_card=agent_card,
        runner=runner,
    )

    # Create request handler
    request_handler = DefaultRequestHandler(
        agent_executor=agent_executor,
        task_store=InMemoryTaskStore(),
    )

    # Create A2A application
    a2a_app = A2AStarletteApplication(
        agent_card=agent_card,
        http_handler=request_handler,
    )

    # Build the app with custom endpoint paths
    app = a2a_app.build(
        agent_card_url="/.well-known/agent.json",
        rpc_url="/a2a/image_processing_agent",
        extended_agent_card_url="/agent/authenticatedExtendedCard"
    )

    # Log server information
    logger.info(f"Starting Image Processing Agent server on http://{host}:{port}")
    logger.info(f"Agent Name: {agent_card.name}, Version: {agent_card.version}")
    logger.info(f"A2A RPC Endpoint: http://{host}:{port}/a2a/image_processing_agent")
    logger.info(f"Agent Card: http://{host}:{port}/.well-known/agent.json")
    logger.info(f"Google Gemini Model: gemini-2.0-flash-exp")
    
    if agent_card.skills:
        logger.info("Available Skills:")
        for skill in agent_card.skills:
            logger.info(f"  • {skill.name} (ID: {skill.id})")
            logger.info(f"    Tags: {', '.join(skill.tags)}")
    
    logger.info("Server supports multipart/form-data file uploads for image processing")
    logger.info("Ready to process images with Google Gemini vision capabilities!")

    # Start the server
    uvicorn.run(app, host=host, port=port)


if __name__ == "__main__":
    main()
