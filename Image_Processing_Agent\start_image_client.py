#!/usr/bin/env python3
"""
Startup script for the A2A Image Processing Client.
Provides easy testing and deployment of the image processing client.
"""

import sys
import os
import subprocess
import time
import requests
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_server_health(url: str, max_retries: int = 10, delay: int = 2) -> bool:
    """Check if the server is running and healthy."""
    for attempt in range(max_retries):
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                logger.info(f"✅ Client server is healthy and responding at {url}")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if attempt < max_retries - 1:
            logger.info(f"⏳ Waiting for client server to start... (attempt {attempt + 1}/{max_retries})")
            time.sleep(delay)
    
    return False


def check_agent_server(agent_url: str) -> bool:
    """Check if the Image Processing Agent server is running."""
    try:
        response = requests.get(f"{agent_url}/.well-known/agent.json", timeout=5)
        if response.status_code == 200:
            logger.info(f"✅ Image Processing Agent is running at {agent_url}")
            return True
        else:
            logger.warning(f"⚠️  Image Processing Agent returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.warning(f"⚠️  Cannot reach Image Processing Agent at {agent_url}: {e}")
        return False


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    try:
        import fastapi
        import uvicorn
        import aiohttp
        logger.info("✅ Core dependencies found")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.error("Please install dependencies with: pip install -r requirements.txt")
        return False


def start_image_client(host: str = "localhost", port: int = 8006, agent_url: str = "http://localhost:8005"):
    """Start the A2A Image Processing Client server."""
    
    # Get the current directory
    current_dir = Path(__file__).parent
    
    logger.info(f"🚀 Starting A2A Image Processing Client on {host}:{port}")
    logger.info(f"📁 Working directory: {current_dir}")
    logger.info(f"🎯 Target Agent: {agent_url}")
    
    try:
        # Start the client server process
        cmd = [
            sys.executable, "image_processing_client.py", 
            "--host", host, 
            "--port", str(port),
            "--agent-url", agent_url
        ]
        logger.info(f"🔧 Running command: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            cwd=current_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Give the server a moment to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            logger.error(f"❌ Client server process exited early")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False
        
        # Check server health
        client_url = f"http://{host}:{port}"
        if check_server_health(client_url):
            logger.info(f"🎉 A2A Image Processing Client is running successfully!")
            logger.info(f"🌐 Client URL: {client_url}")
            logger.info(f"📋 Upload endpoint: {client_url}/upload")
            logger.info(f"📖 API docs: {client_url}/docs")
            logger.info(f"🔗 Target agent: {agent_url}")
            logger.info(f"📋 Use Ctrl+C to stop the client")
            
            # Show Postman testing instructions
            logger.info("")
            logger.info("📮 POSTMAN TESTING INSTRUCTIONS:")
            logger.info("1. Set method to POST")
            logger.info(f"2. Set URL to {client_url}/upload")
            logger.info("3. Go to Body tab → form-data")
            logger.info("4. Add key 'files' with type 'File'")
            logger.info("5. Select multiple image files")
            logger.info("6. Optionally add 'user_id' and 'description' as text fields")
            logger.info("7. Send the request")
            logger.info("")
            
            try:
                # Keep the server running and show logs
                while True:
                    # Read and display server output
                    output = process.stdout.readline()
                    if output:
                        print(f"[CLIENT] {output.strip()}")
                    
                    # Check if process is still alive
                    if process.poll() is not None:
                        logger.warning("⚠️  Client server process has stopped")
                        break
                        
                    time.sleep(0.1)
                    
            except KeyboardInterrupt:
                logger.info("🛑 Stopping client server...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("⚠️  Force killing client server process")
                    process.kill()
                logger.info("✅ Client server stopped")
                return True
        else:
            logger.error(f"❌ Client server failed to start or is not responding")
            process.terminate()
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to start client server: {e}")
        return False


def main():
    """Main function."""
    print("A2A Image Processing Client Starter")
    print("=" * 40)
    print("This script starts the A2A Image Processing Client for testing.")
    print("The client will run on http://localhost:8006")
    print()
    
    # Check if we're in the right directory
    current_dir = Path(__file__).parent
    client_file = current_dir / "image_processing_client.py"
    
    if not client_file.exists():
        print(f"❌ Error: {client_file} not found!")
        print("Please run this script from the Image_Processing_Agent directory.")
        sys.exit(1)
    
    print("🔍 Running prerequisite checks...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check if Image Processing Agent is running
    agent_url = "http://localhost:8005"
    agent_running = check_agent_server(agent_url)
    
    if not agent_running:
        print("⚠️  WARNING: Image Processing Agent is not running!")
        print("Please start the agent first with:")
        print("  python start_image_processing_server.py")
        print()
        response = input("Continue anyway? (y/N): ").strip().lower()
        if response != 'y':
            print("Exiting. Please start the Image Processing Agent first.")
            sys.exit(1)
    
    print("✅ Prerequisites check passed")
    print("🚀 Starting A2A Image Processing Client...")
    print()
    
    success = start_image_client()
    
    if success:
        print("\n🎉 Client session completed successfully")
    else:
        print("\n❌ Client failed to start or encountered an error")
        sys.exit(1)


if __name__ == "__main__":
    main()
