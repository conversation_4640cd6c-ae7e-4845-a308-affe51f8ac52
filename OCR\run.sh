#!/bin/bash
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Startup script for the OCR Web Search Agent

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}OCR Web Search Agent Startup Script${NC}"
echo "======================================"

# Check if Python 3.10+ is available
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.10"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo -e "${RED}Error: Python 3.10 or higher is required. Found: $python_version${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Python version check passed: $python_version${NC}"

# Check if Node.js is available (required for npx)
if ! command -v node &> /dev/null; then
    echo -e "${RED}Error: Node.js is required for Firecrawl MCP server${NC}"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo -e "${GREEN}✓ Node.js is available${NC}"

# Check if npx is available
if ! command -v npx &> /dev/null; then
    echo -e "${RED}Error: npx is required for Firecrawl MCP server${NC}"
    echo "Please install npm/npx"
    exit 1
fi

echo -e "${GREEN}✓ npx is available${NC}"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}Creating virtual environment...${NC}"
    python3 -m venv venv
fi

# Activate virtual environment
echo -e "${YELLOW}Activating virtual environment...${NC}"
source venv/bin/activate || source venv/Scripts/activate

# Install dependencies
echo -e "${YELLOW}Installing dependencies...${NC}"
pip install -r requirements.txt

# Copy .env.example to .env if .env doesn't exist
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}Creating .env file from .env.example...${NC}"
    cp .env.example .env
    echo -e "${YELLOW}Please edit .env file with your API keys if needed${NC}"
fi

# Start the server
echo -e "${GREEN}Starting OCR Web Search Agent server...${NC}"
python3 start.py "$@"
