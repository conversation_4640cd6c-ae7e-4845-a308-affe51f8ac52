# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Configuration settings for the OCR Web Search Agent."""

import os
from typing import Optional

from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for the OCR Web Search Agent."""
    
    # Firecrawl MCP Configuration
    FIRECRAWL_API_KEY: str = os.getenv(
        "FIRECRAWL_API_KEY", 
        "fc-9c8bbdeff8d248dbaec414064b0b7a9d"
    )
    
    # Google Gemini Configuration
    GOOGLE_API_KEY: str = os.getenv(
        "GOOGLE_API_KEY", 
        "AIzaSyB5jvOM9xD7fvDs8C5b4T2N-u58chACTo0"
    )
    
    # MCP Server Configuration
    MCP_COMMAND: str = "npx"
    MCP_ARGS: list[str] = ["-y", "firecrawl-mcp"]
    
    # Server Configuration
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    
    # Agent Configuration
    AGENT_NAME: str = "ocr_web_search_agent"
    AGENT_DESCRIPTION: str = "A web search agent that can perform web searches and extract content using Firecrawl"
    
    # Gemini Model Configuration
    GEMINI_MODEL: str = "gemini-2.0-flash"
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def validate(cls) -> None:
        """Validate that required configuration is present."""
        if not cls.FIRECRAWL_API_KEY:
            raise ValueError("FIRECRAWL_API_KEY is required")
        if not cls.GOOGLE_API_KEY:
            raise ValueError("GOOGLE_API_KEY is required")


# Global config instance
config = Config()
