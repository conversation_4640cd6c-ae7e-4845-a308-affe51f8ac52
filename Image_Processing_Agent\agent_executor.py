"""
Image Processing Agent Executor for A2A protocol compliance.
Handles multipart/form-data file uploads and processes images with Gemini vision.
"""

import logging
import uuid
import json
import base64
import tempfile
import os
from typing import List, Dict, Any, Optional
from pathlib import Path
from collections.abc import AsyncGenerator

from a2a.server.agent_execution import Agent<PERSON>xecutor, RequestContext
from a2a.server.events import EventQueue
from a2a.types import (
    AgentCard,
    TaskState,
    TaskStatus,
    TaskStatusUpdateEvent,
)

from a2a.utils import new_agent_text_message
from google.adk.agents import Agent
from google.adk.events import Event
from google.adk.runners import Runner
from google.adk.sessions import Session as ADKSession
from google.genai import types as adk_types

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageProcessingExecutor(AgentExecutor):
    """
    A2A Agent Executor for image processing using Google Gemini vision capabilities.
    
    This executor:
    - Handles multipart/form-data file uploads containing images
    - Processes images using Gemini's vision capabilities
    - Generates A2A protocol compliant task IDs, user IDs, context IDs, and timestamps
    - Returns structured responses about image content
    """

    def __init__(self, agent: Agent, agent_card: AgentCard, runner: Runner):
        self.agent = agent
        self.agent_card = agent_card
        self.runner = runner
        self.session_service = runner.session_service
        self.artifact_service = runner.artifact_service
        
        logger.info(
            f"Image Processing Executor initialized for agent '{self.agent.name}' "
            f"with app '{self.runner.app_name}'"
        )

    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        """
        Execute image processing for the given request context.
        
        Args:
            context: Request context containing image data and metadata
            event_queue: Event queue for publishing A2A protocol events
        """
        try:
            # Step 1: Prepare image input for processing
            processed_input = await self._prepare_image_input(context)
            
            # Step 2: Set up session identifiers for A2A protocol
            user_id, session_id = self._generate_session_identifiers(context)
            await self._ensure_adk_session(user_id, session_id)
            
            # Step 3: Process images with Gemini vision
            analysis_result = await self._process_images_with_gemini(
                processed_input, user_id, session_id
            )
            
            # Step 4: Send structured response back via A2A protocol
            await self._send_response(event_queue, context, analysis_result)
            
        except Exception as e:
            await self._handle_error(event_queue, context, e)

    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        """Cancel the image processing execution."""
        logger.info(f"Cancelling image processing for task {context.task_id}")
        # For image processing, cancellation is immediate since it's typically fast
        pass

    async def _prepare_image_input(self, context: RequestContext) -> Dict[str, Any]:
        """
        Prepare and validate image input from the request context.
        
        Args:
            context: Request context containing image data
            
        Returns:
            Dict containing processed image information
        """
        user_input = context.get_user_input()
        if not user_input:
            logger.warning("No user input found; using default message")
            return {
                "images": [],
                "message": "No images provided for processing"
            }

        try:
            # Try to parse as JSON for structured image data
            parsed_input = json.loads(user_input)
            
            if isinstance(parsed_input, dict) and "images" in parsed_input:
                # Structured input with image array
                images = parsed_input["images"]
                message = parsed_input.get("message", "Process these images")
                
                logger.info(f"Processing {len(images)} images from structured input")
                return {
                    "images": await self._process_image_array(images),
                    "message": message
                }
            elif isinstance(parsed_input, list):
                # Direct image array
                logger.info(f"Processing {len(parsed_input)} images from array")
                return {
                    "images": await self._process_image_array(parsed_input),
                    "message": "Process these images"
                }
            else:
                # Single image or text input
                return {
                    "images": [],
                    "message": str(parsed_input)
                }
                
        except (json.JSONDecodeError, TypeError):
            # Not JSON, treat as text input
            logger.info("Processing text input")
            return {
                "images": [],
                "message": user_input
            }

    async def _process_image_array(self, images: List[Any]) -> List[Dict[str, Any]]:
        """
        Process an array of image data into structured format.
        
        Args:
            images: List of image data (can be file paths, base64, or metadata)
            
        Returns:
            List of processed image information
        """
        processed_images = []
        
        for i, image_data in enumerate(images):
            try:
                image_info = await self._process_single_image(image_data, i)
                if image_info:
                    processed_images.append(image_info)
            except Exception as e:
                logger.error(f"Error processing image {i}: {e}")
                # Add error entry for failed image
                processed_images.append({
                    "id": str(uuid.uuid4()),
                    "index": i,
                    "error": f"Failed to process image: {str(e)}",
                    "original_data": str(image_data)[:100]  # Truncated for logging
                })
        
        return processed_images

    async def _process_single_image(self, image_data: Any, index: int) -> Optional[Dict[str, Any]]:
        """
        Process a single image from the input data.

        Args:
            image_data: Image data (file path, base64, or metadata dict)
            index: Index of the image in the array

        Returns:
            Processed image information or None if processing fails
        """
        image_id = str(uuid.uuid4())

        if isinstance(image_data, dict):
            # Structured image data with metadata
            return {
                "id": image_id,
                "index": index,
                "original_name": image_data.get("name", f"Image_{index + 1}"),
                "file_path": image_data.get("path", image_data.get("file")),
                "base64_data": image_data.get("data", image_data.get("base64")),
                "mime_type": image_data.get("mime_type", image_data.get("type", "image/jpeg")),
                "description": image_data.get("description", "Image for processing"),
                "size": image_data.get("size", 0)
            }
        elif isinstance(image_data, str):
            # Could be file path or base64 data
            if image_data.startswith("data:image/") or len(image_data) > 100:
                # Likely base64 data
                return {
                    "id": image_id,
                    "index": index,
                    "original_name": f"Image_{index + 1}",
                    "base64_data": image_data,
                    "mime_type": "image/jpeg",
                    "description": "Base64 encoded image",
                    "size": len(image_data)
                }
            else:
                # Likely file path
                return {
                    "id": image_id,
                    "index": index,
                    "original_name": Path(image_data).name if Path(image_data).name else f"Image_{index + 1}",
                    "file_path": image_data,
                    "mime_type": "image/jpeg",
                    "description": "Image file for processing",
                    "size": 0
                }
        else:
            # Unknown format
            logger.warning(f"Unknown image data format for index {index}: {type(image_data)}")
            return None

    def _generate_session_identifiers(self, context: RequestContext) -> tuple[str, str]:
        """
        Generate A2A protocol compliant user_id and session_id.

        Args:
            context: Request context

        Returns:
            Tuple of (user_id, session_id)
        """
        user_id = "a2a_user_image_processing"
        session_id = context.task_id or str(uuid.uuid4())
        return user_id, session_id

    async def _ensure_adk_session(self, user_id: str, session_id: str) -> None:
        """
        Create or retrieve ADK session for the image processing agent.

        Args:
            user_id: User identifier
            session_id: Session identifier
        """
        adk_session: ADKSession | None = await self.session_service.get_session(
            app_name=self.runner.app_name, user_id=user_id, session_id=session_id
        )

        if not adk_session:
            await self.session_service.create_session(
                app_name=self.runner.app_name,
                user_id=user_id,
                session_id=session_id,
                state={},
            )
            logger.info(f"Created new ADK session: {session_id} for {self.agent.name}")

    async def _process_images_with_gemini(
        self, processed_input: Dict[str, Any], user_id: str, session_id: str
    ) -> str:
        """
        Process images using Gemini's vision capabilities.

        Args:
            processed_input: Processed image input data
            user_id: User identifier
            session_id: Session identifier

        Returns:
            Structured analysis result as JSON string
        """
        images = processed_input["images"]
        message = processed_input["message"]

        if not images:
            # No images to process, return text-only response
            return json.dumps({
                "status": "success",
                "processed_images": 0,
                "message": "No images provided for processing",
                "results": []
            }, indent=2)

        # Create instruction for Gemini with image information
        instruction = self._create_gemini_instruction(images, message)

        # Prepare content for Gemini (text + images)
        content_parts = [adk_types.Part(text=instruction)]

        # Add image parts to content
        for image_info in images:
            image_part = await self._create_image_part(image_info)
            if image_part:
                content_parts.append(image_part)

        request_content = adk_types.Content(
            role="user", parts=content_parts
        )

        logger.debug(f"Running Gemini vision analysis with {len(images)} images")

        # Run the agent with multimodal content
        events_async: AsyncGenerator[Event, None] = self.runner.run_async(
            user_id=user_id, session_id=session_id, new_message=request_content
        )

        final_response = "(No analysis results found)"

        async for event in events_async:
            if (
                event.is_final_response()
                and event.content
                and event.content.role == "model"
            ):
                if event.content.parts and event.content.parts[0].text:
                    final_response = event.content.parts[0].text
                    logger.info(
                        f"Gemini analysis complete: {len(final_response)} characters"
                    )
                    break

        # Format and validate the response
        return self._format_analysis_response(final_response, len(images))

    def _create_gemini_instruction(self, images: List[Dict[str, Any]], message: str) -> str:
        """
        Create instruction text for Gemini vision analysis.

        Args:
            images: List of processed image information
            message: User message or instruction

        Returns:
            Formatted instruction for Gemini
        """
        instruction = f"""
        {message}

        I have {len(images)} image(s) to analyze. Please process each image and provide a comprehensive analysis following the specified JSON format.

        For each image, provide:
        1. Detailed content description
        2. All visible text extraction (OCR)
        3. Visual elements identification
        4. Context and purpose analysis
        5. Quality assessment
        6. Suggested tags and metadata

        Image details:
        """

        for i, img in enumerate(images):
            instruction += f"""
        Image {i+1}:
        - ID: {img['id']}
        - Original Name: {img['original_name']}
        - Description: {img.get('description', 'Image for processing')}
        """

        instruction += """

        Please analyze all images and return a valid JSON response with the specified structure.
        """

        return instruction

    async def _create_image_part(self, image_info: Dict[str, Any]) -> Optional[adk_types.Part]:
        """
        Create an image part for Gemini from image information.

        Args:
            image_info: Processed image information

        Returns:
            Image part for Gemini or None if creation fails
        """
        try:
            # Try to get image data
            if "base64_data" in image_info and image_info["base64_data"]:
                # Use base64 data directly
                base64_data = image_info["base64_data"]
                if base64_data.startswith("data:image/"):
                    # Remove data URL prefix
                    base64_data = base64_data.split(",", 1)[1]

                return adk_types.Part(
                    inline_data=adk_types.Blob(
                        mime_type=image_info.get("mime_type", "image/jpeg"),
                        data=base64_data
                    )
                )
            elif "file_path" in image_info and image_info["file_path"]:
                # Read file and convert to base64
                file_path = image_info["file_path"]
                if os.path.exists(file_path):
                    with open(file_path, "rb") as f:
                        image_bytes = f.read()
                        base64_data = base64.b64encode(image_bytes).decode()

                    return adk_types.Part(
                        inline_data=adk_types.Blob(
                            mime_type=image_info.get("mime_type", "image/jpeg"),
                            data=base64_data
                        )
                    )
                else:
                    logger.warning(f"Image file not found: {file_path}")
                    return None
            else:
                logger.warning(f"No valid image data found for image {image_info.get('id')}")
                return None

        except Exception as e:
            logger.error(f"Error creating image part: {e}")
            return None

    def _format_analysis_response(self, response_text: str, image_count: int) -> str:
        """
        Format and validate the analysis response from Gemini.

        Args:
            response_text: Raw response from Gemini
            image_count: Number of images processed

        Returns:
            Formatted JSON response
        """
        try:
            # Try to parse as JSON to validate structure
            parsed_response = json.loads(response_text)

            # Ensure proper structure
            if isinstance(parsed_response, dict) and "results" in parsed_response:
                # Response is already in correct format
                parsed_response["status"] = "success"
                parsed_response["processed_images"] = len(parsed_response.get("results", []))
                return json.dumps(parsed_response, indent=2)
            elif isinstance(parsed_response, list):
                # Response is a list of results
                return json.dumps({
                    "status": "success",
                    "processed_images": len(parsed_response),
                    "results": parsed_response
                }, indent=2)
            else:
                # Wrap single result
                return json.dumps({
                    "status": "success",
                    "processed_images": 1,
                    "results": [parsed_response]
                }, indent=2)

        except (json.JSONDecodeError, TypeError):
            # Not valid JSON, wrap as text result
            return json.dumps({
                "status": "partial_success",
                "processed_images": image_count,
                "message": "Analysis completed but response format needs adjustment",
                "raw_response": response_text,
                "results": [{
                    "id": str(uuid.uuid4()),
                    "content_description": response_text,
                    "analysis_note": "Raw response from vision model"
                }]
            }, indent=2)

    async def _send_response(
        self, event_queue: EventQueue, context: RequestContext, analysis_result: str
    ) -> None:
        """
        Send the analysis response back via the A2A event queue.

        Args:
            event_queue: Event queue for A2A protocol
            context: Request context
            analysis_result: Formatted analysis result
        """
        logger.info(f"Sending image analysis response for task {context.task_id}")
        await event_queue.enqueue_event(
            new_agent_text_message(
                text=analysis_result,
                context_id=context.context_id,
                task_id=context.task_id,
            )
        )

    async def _handle_error(
        self, event_queue: EventQueue, context: RequestContext, error: Exception
    ) -> None:
        """
        Handle errors and send error response via A2A protocol.

        Args:
            event_queue: Event queue for A2A protocol
            context: Request context
            error: Exception that occurred
        """
        logger.error(
            f"Error in image processing agent {self.agent.name}: {str(error)}",
            exc_info=True,
        )

        error_response = json.dumps({
            "status": "error",
            "processed_images": 0,
            "error_message": str(error),
            "error_type": type(error).__name__,
            "results": []
        }, indent=2)

        await event_queue.enqueue_event(
            new_agent_text_message(
                text=error_response,
                context_id=context.context_id,
                task_id=context.task_id,
            )
        )
