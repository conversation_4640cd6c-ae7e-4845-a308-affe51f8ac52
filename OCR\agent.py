# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""OCR Web Search Agent implementation using Google ADK and Firecrawl MCP."""

import logging
import os
from typing import Optional

from google.adk.agents.llm_agent import LlmAgent
from google.genai import types

from config import config
from mcp_integration import create_firecrawl_mcp_toolset, get_firecrawl_tools_description

logger = logging.getLogger(__name__)


def create_web_search_agent() -> LlmAgent:
    """Create and configure the OCR Web Search Agent.
    
    Returns:
        LlmAgent: Configured web search agent with Firecrawl MCP integration.
    """
    # Set up Google API key for Gemini
    if config.GOOGLE_API_KEY:
        os.environ["GOOGLE_API_KEY"] = config.GOOGLE_API_KEY
    
    # Create the Firecrawl MCP toolset
    firecrawl_toolset = create_firecrawl_mcp_toolset()
    
    # Define the agent instruction
    instruction = f"""
    You are a helpful web search assistant powered by Firecrawl technology. Your primary role is to help users 
    search the web, extract content from websites, and provide comprehensive information based on web sources.
    
    {get_firecrawl_tools_description()}
    
    Guidelines for your responses:
    1. Always use the available Firecrawl tools to search for and extract web content when users ask questions
    2. Provide accurate, up-to-date information based on the web content you retrieve
    3. Cite sources when possible by mentioning the URLs you accessed
    4. If you cannot find relevant information, explain what you searched for and suggest alternative approaches
    5. Be helpful, accurate, and transparent about your search process
    6. Handle errors gracefully and provide useful feedback to users
    
    You excel at:
    - Web searches for current information
    - Content extraction from specific websites
    - Summarizing web content
    - Finding and comparing information from multiple sources
    - Helping users navigate and understand web content
    """
    
    # Create the LLM agent with Gemini model
    agent = LlmAgent(
        model=config.GEMINI_MODEL,
        name=config.AGENT_NAME,
        description=config.AGENT_DESCRIPTION,
        instruction=instruction,
        tools=[firecrawl_toolset],
        generate_content_config=types.GenerateContentConfig(
            temperature=0.1,  # Lower temperature for more consistent responses
            response_modalities=[types.Modality.TEXT],
            safety_settings=[
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
            ],
        ),
    )
    
    logger.info(f"Created web search agent: {config.AGENT_NAME}")
    return agent


# Create the root agent instance
root_agent = create_web_search_agent()
