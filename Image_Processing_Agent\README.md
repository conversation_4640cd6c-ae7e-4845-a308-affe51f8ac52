# Image Processing Agent

An A2A (Agent-to-Agent) compliant server that provides advanced image processing capabilities using Google Gemini's vision model. This agent can analyze images, extract text (OCR), identify objects, and provide comprehensive structured responses about image content.

## Features

- **Google Gemini Vision**: Powered by Gemini 2.0 Flash model for state-of-the-art image analysis
- **A2A Protocol Compliance**: Full support for Agent-to-Agent communication standards
- **Multipart File Upload**: Accepts images via multipart/form-data uploads
- **Comprehensive Analysis**: Provides detailed image analysis including:
  - Text extraction (OCR)
  - Object identification
  - Scene understanding
  - Content categorization
  - Quality assessment
  - Metadata generation
- **Structured Responses**: Returns well-formatted JSON with detailed analysis results
- **Multiple Image Support**: Process multiple images in a single request
- **Format Support**: PNG, JPEG, GIF, WebP, BMP, TIFF

## Quick Start

### Prerequisites

1. Python 3.8 or higher
2. Google Gemini API key
3. Required dependencies (see requirements.txt)

### Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your Google API key in the `.env` file or environment:
```bash
export GOOGLE_API_KEY="your-api-key-here"
```

### Running the System

#### Option 1: Complete System (Agent + Client)

1. **Start the Image Processing Agent** (Terminal 1):
```bash
python start_image_processing_server.py
```

2. **Start the A2A Client** (Terminal 2):
```bash
python start_image_client.py
```

3. **Test with Postman**:
   - POST to `http://localhost:8006/upload`
   - Use form-data with key `files` (type: File)
   - Upload image files and get comprehensive analysis

#### Option 2: Agent Only (Direct A2A Protocol)
```bash
python start_image_processing_server.py
```

The agent will start on `http://localhost:8005` by default.
The client will start on `http://localhost:8006` by default.

## API Endpoints

### A2A Image Processing Agent (Port 8005)

- **GET** `/.well-known/agent.json` - Returns agent capabilities and metadata
- **POST** `/a2a/image_processing_agent` - Main A2A communication endpoint
- **GET** `/agent/authenticatedExtendedCard` - Extended agent information

### A2A Client (Port 8006)

- **GET** `/` - Information page with usage instructions
- **POST** `/upload` - Upload images via multipart/form-data (for Postman testing)
- **GET** `/health` - Health check endpoint
- **GET** `/docs` - Interactive API documentation (Swagger UI)

## Usage Examples

### Easy Testing with Postman (Recommended)

1. **Start both servers** (see Quick Start above)
2. **Open Postman** and create a POST request to `http://localhost:8006/upload`
3. **Set Body to form-data** and add:
   - Key: `files` (Type: File) - Select your image files
   - Key: `user_id` (Type: Text) - Optional user identifier
   - Key: `description` (Type: Text) - Optional description
4. **Send the request** and get comprehensive image analysis

### Direct A2A Protocol (Advanced)

Send a JSON-RPC request to `/a2a/image_processing_agent`:

```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "message/send",
  "params": {
    "task_id": "unique-task-id",
    "context_id": "conversation-context",
    "user_id": "user-identifier",
    "timestamp": 1704067200000,
    "message": {
      "messageId": "msg-123",
      "role": "user",
      "parts": [
        {
          "text": "{\"images\":[{\"name\":\"document.png\",\"data\":\"base64-data\",\"mime_type\":\"image/png\"}],\"message\":\"Analyze this image\"}"
        }
      ]
    }
  }
}
```

### Response Format

The agent returns structured JSON responses:

```json
{
  "status": "success",
  "processed_images": 1,
  "results": [
    {
      "id": "unique-image-id",
      "original_name": "document.png",
      "updated_name": "Financial_Report_Q3_2024",
      "content_description": "A financial report document with tables and charts",
      "extracted_text": "Q3 2024 Financial Report...",
      "visual_elements": {
        "objects": ["table", "chart", "logo"],
        "layout": "Professional document layout with header and sections",
        "colors": ["blue", "white", "gray"],
        "style": "Corporate financial document"
      },
      "context_analysis": "This appears to be a quarterly financial report",
      "quality_assessment": {
        "clarity": "High quality, clear text",
        "resolution": "Good resolution for text extraction",
        "issues": "None detected"
      },
      "suggested_tags": ["financial", "report", "quarterly", "business"],
      "confidence_score": 0.95
    }
  ]
}
```

## Configuration

### Environment Variables

- `GOOGLE_API_KEY`: Your Google Gemini API key (required)
- `A2A_IMAGE_HOST`: Server host (default: localhost)
- `A2A_IMAGE_PORT`: Server port (default: 8005)
- `LOG_LEVEL`: Logging level (default: INFO)

### Agent Configuration

The agent is configured with:
- **Model**: gemini-2.0-flash-exp
- **Temperature**: 0.1 (for consistent, factual analysis)
- **Max Output Tokens**: 4096
- **Vision Capabilities**: Enabled

## Skills

The agent provides three main skills:

1. **Image Analysis**: Comprehensive visual content analysis
2. **Content Extraction**: OCR and text extraction capabilities  
3. **Visual Understanding**: Object and scene recognition

## File Structure

```
Image_Processing_Agent/
├── __init__.py                        # Package initialization
├── agent.py                           # Agent configuration with Gemini
├── agent_executor.py                  # A2A executor implementation
├── prompt.py                          # Agent prompt template
├── _main_.py                          # Agent server entry point
├── image_processing_client.py         # A2A client for HTTP file uploads
├── agent.json                         # Agent card configuration
├── requirements.txt                   # Python dependencies
├── .env                              # Environment configuration
├── start_image_processing_server.py  # Agent server startup script
├── start_image_client.py             # Client server startup script
├── TESTING_GUIDE.md                  # Detailed testing instructions
└── README.md                         # This documentation
```

## Development

### Testing

The server includes health checks and comprehensive error handling. Test the server by:

1. Starting the server
2. Checking the agent card: `GET http://localhost:8005/.well-known/agent.json`
3. Sending test image analysis requests

### Logging

The agent provides detailed logging for:
- Server startup and configuration
- Image processing requests
- Analysis results
- Error handling

## Integration

This agent follows A2A protocol standards and can be integrated with:
- Other A2A compliant agents
- Agent orchestration systems
- Workflow automation tools
- Image processing pipelines

## License

This project follows the same license as the parent A2A Python SDK.

## Support

For issues and questions:
1. Check the logs for detailed error information
2. Verify your Google API key is valid
3. Ensure all dependencies are properly installed
4. Review the A2A protocol documentation
