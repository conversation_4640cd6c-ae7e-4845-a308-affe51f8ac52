#!/usr/bin/env python3
"""
Startup script for the Enhanced Image Processing Agent server with cursor tracking.
Supports both Gemini and OpenAI models for video frame analysis.
"""

import sys
import os
import subprocess
import time
import requests
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_server_health(url: str, max_retries: int = 10, delay: int = 2) -> bool:
    """Check if the server is running and healthy."""
    for attempt in range(max_retries):
        try:
            response = requests.get(f"{url}/.well-known/agent.json", timeout=5)
            if response.status_code == 200:
                logger.info(f"✅ Enhanced server is healthy and responding at {url}")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if attempt < max_retries - 1:
            logger.info(f"⏳ Waiting for enhanced server to start... (attempt {attempt + 1}/{max_retries})")
            time.sleep(delay)
    
    return False


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    try:
        import google.adk
        import a2a
        import fastapi
        import uvicorn
        import cv2
        import numpy
        logger.info("✅ Core dependencies found")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.error("Please install dependencies with: pip install -r requirements.txt")
        return False


def check_api_keys() -> tuple[bool, str]:
    """Check if API keys are available and determine which model to use."""
    from dotenv import load_dotenv
    
    # Load environment variables
    env_file = Path(__file__).parent / ".env"
    if env_file.exists():
        load_dotenv(env_file)
    
    # Check for model preference
    vision_model = os.getenv("VISION_MODEL", "gemini").lower()
    
    if vision_model == "openai":
        # Check OpenAI API key
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key and openai_key != "your-openai-api-key-here":
            logger.info("✅ OpenAI API key found - using GPT-4o-mini")
            return True, "openai"
        else:
            logger.error("❌ OpenAI API key not found or invalid")
            logger.error("Please set OPENAI_API_KEY in environment or .env file")
            return False, "openai"
    else:
        # Check Gemini API key (default)
        gemini_key = os.getenv("GOOGLE_API_KEY")
        if gemini_key and gemini_key.startswith("AIza"):
            logger.info("✅ Google Gemini API key found - using Gemini 2.0 Flash")
            return True, "gemini"
        else:
            logger.error("❌ Google Gemini API key not found or invalid")
            logger.error("Please set GOOGLE_API_KEY in environment or .env file")
            return False, "gemini"


def start_enhanced_server(host: str = "localhost", port: int = 8005, model: str = "gemini"):
    """Start the Enhanced Image Processing Agent server."""
    
    # Get the current directory
    current_dir = Path(__file__).parent
    
    logger.info(f"🚀 Starting Enhanced Image Processing Agent on {host}:{port}")
    logger.info(f"📁 Working directory: {current_dir}")
    logger.info(f"🤖 Using model: {model.upper()}")
    
    try:
        # Create the startup command
        if model == "openai":
            # Use OpenAI-based agent
            cmd = [
                sys.executable, "-c", 
                """
import sys
sys.path.insert(0, '.')
from enhanced_agent_executor import EnhancedImageProcessingExecutor
from agent_openai import create_openai_image_processing_agent
from _main_ import main
import os
os.environ['USE_ENHANCED_EXECUTOR'] = 'true'
os.environ['USE_OPENAI_AGENT'] = 'true'
main()
                """,
                "--host", host, "--port", str(port)
            ]
        else:
            # Use Gemini-based agent (default)
            cmd = [
                sys.executable, "-c",
                """
import sys
sys.path.insert(0, '.')
from enhanced_agent_executor import EnhancedImageProcessingExecutor
from agent import create_image_processing_agent
from _main_ import main
import os
os.environ['USE_ENHANCED_EXECUTOR'] = 'true'
main()
                """,
                "--host", host, "--port", str(port)
            ]
        
        logger.info(f"🔧 Starting enhanced server with {model.upper()} model...")
        
        process = subprocess.Popen(
            cmd,
            cwd=current_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Give the server a moment to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            logger.error(f"❌ Enhanced server process exited early")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False
        
        # Check server health
        server_url = f"http://{host}:{port}"
        if check_server_health(server_url):
            logger.info(f"🎉 Enhanced Image Processing Agent is running successfully!")
            logger.info(f"🌐 Agent card: {server_url}/.well-known/agent.json")
            logger.info(f"🔗 A2A endpoint: {server_url}/a2a/image_processing_agent")
            logger.info(f"🖼️  Ready to process video frames with cursor tracking!")
            logger.info(f"🎯 Model: {model.upper()}")
            logger.info(f"📋 Use Ctrl+C to stop the server")
            
            # Show enhanced features
            logger.info("")
            logger.info("🔥 ENHANCED FEATURES:")
            logger.info("• Mouse cursor detection and tracking")
            logger.info("• Browser URL extraction from address bars")
            logger.info("• UI element identification and coordinates")
            logger.info("• Video frame temporal analysis")
            logger.info("• Template matching for cursor tracking")
            logger.info("• ADK memory integration for movement analysis")
            logger.info("")
            
            try:
                # Keep the server running and show logs
                while True:
                    # Read and display server output
                    output = process.stdout.readline()
                    if output:
                        print(f"[ENHANCED] {output.strip()}")
                    
                    # Check if process is still alive
                    if process.poll() is not None:
                        logger.warning("⚠️  Enhanced server process has stopped")
                        break
                        
                    time.sleep(0.1)
                    
            except KeyboardInterrupt:
                logger.info("🛑 Stopping enhanced server...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("⚠️  Force killing enhanced server process")
                    process.kill()
                logger.info("✅ Enhanced server stopped")
                return True
        else:
            logger.error(f"❌ Enhanced server failed to start or is not responding")
            process.terminate()
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to start enhanced server: {e}")
        return False


def main():
    """Main function."""
    print("Enhanced Image Processing Agent Server Starter")
    print("=" * 50)
    print("This script starts the Enhanced Image Processing Agent with cursor tracking.")
    print("Supports both Gemini 2.0 Flash and OpenAI GPT-4o-mini models.")
    print()
    
    # Check if we're in the right directory
    current_dir = Path(__file__).parent
    main_file = current_dir / "_main_.py"
    
    if not main_file.exists():
        print(f"❌ Error: {main_file} not found!")
        print("Please run this script from the Image_Processing_Agent directory.")
        sys.exit(1)
    
    print("🔍 Running prerequisite checks...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check API keys and determine model
    api_keys_ok, model = check_api_keys()
    if not api_keys_ok:
        sys.exit(1)
    
    print("✅ Prerequisites check passed")
    print(f"🚀 Starting Enhanced Image Processing Agent with {model.upper()} model...")
    print()
    
    success = start_enhanced_server(model=model)
    
    if success:
        print("\n🎉 Enhanced server session completed successfully")
    else:
        print("\n❌ Enhanced server failed to start or encountered an error")
        sys.exit(1)


if __name__ == "__main__":
    main()
