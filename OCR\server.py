# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""A2A Server implementation for the OCR Web Search Agent."""

import asyncio
import json
import logging
import os
from pathlib import Path
from typing import Optional

import uvicorn
from fastapi import FastAPI
from google.adk.a2a.executor.a2a_agent_executor import A2AAgentExecutor

from agent import root_agent
from config import config

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_agent_card() -> dict:
    """Load the agent card configuration from agent.json.
    
    Returns:
        dict: Agent card configuration.
    """
    agent_card_path = Path(__file__).parent / "agent.json"
    with open(agent_card_path, 'r') as f:
        return json.load(f)


def create_a2a_server() -> FastAPI:
    """Create and configure the A2A FastAPI server.
    
    Returns:
        FastAPI: Configured A2A server application.
    """
    # Create FastAPI app
    app = FastAPI(
        title="OCR Web Search Agent",
        description="A2A server for web search agent with Firecrawl MCP integration",
        version="1.0.0"
    )
    
    # Load agent card
    agent_card = load_agent_card()
    
    # Create A2A agent executor
    executor = A2AAgentExecutor(
        agent=root_agent,
        agent_card=agent_card
    )
    
    # Add A2A routes to the FastAPI app
    executor.add_routes_to_app(
        app=app,
        agent_card_url="/.well-known/agent.json",
        rpc_url="/a2a/ocr_web_search_agent",
        extended_agent_card_url="/agent/authenticatedExtendedCard"
    )
    
    # Add health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "healthy", "agent": config.AGENT_NAME}
    
    # Add root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with agent information."""
        return {
            "message": "OCR Web Search Agent A2A Server",
            "agent": config.AGENT_NAME,
            "description": config.AGENT_DESCRIPTION,
            "version": "1.0.0",
            "endpoints": {
                "agent_card": "/.well-known/agent.json",
                "a2a_rpc": "/a2a/ocr_web_search_agent",
                "health": "/health"
            }
        }
    
    logger.info(f"A2A server created for agent: {config.AGENT_NAME}")
    return app


def run_server():
    """Run the A2A server."""
    # Validate configuration
    config.validate()
    
    # Create the server
    app = create_a2a_server()
    
    # Run the server
    logger.info(f"Starting A2A server on {config.HOST}:{config.PORT}")
    uvicorn.run(
        app,
        host=config.HOST,
        port=config.PORT,
        log_level=config.LOG_LEVEL.lower()
    )


if __name__ == "__main__":
    run_server()
