{"info": {"name": "OCR File Upload Tests", "description": "Test collection for OCR Agent file upload functionality using multipart/form-data", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "upload_client_url", "value": "http://localhost:8004", "type": "string"}, {"key": "ocr_agent_url", "value": "http://localhost:8003", "type": "string"}], "item": [{"name": "1. Upload Client Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{upload_client_url}}/health", "host": ["{{upload_client_url}}"], "path": ["health"]}, "description": "Check if the file upload client is running and healthy"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Upload client is healthy\", function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', 'healthy');", "    pm.expect(jsonData).to.have.property('service', 'A2A File Upload Client');", "});"], "type": "text/javascript"}}]}, {"name": "2. OCR Agent Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{ocr_agent_url}}/.well-known/agent.json", "host": ["{{ocr_agent_url}}"], "path": [".well-known", "agent.json"]}, "description": "Verify the OCR agent is running and accessible"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"OCR agent is accessible\", function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('name');", "    pm.expect(jsonData.name).to.include('OCR');", "});"], "type": "text/javascript"}}]}, {"name": "3. Single File Upload", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": [], "description": "Select an image file to upload (JPG, PNG, PDF, etc.)"}, {"key": "user_id", "value": "test_user_single", "type": "text", "description": "Custom user ID for tracking"}, {"key": "description", "value": "Single file OCR test", "type": "text", "description": "Description of the processing task"}]}, "url": {"raw": "{{upload_client_url}}/upload", "host": ["{{upload_client_url}}"], "path": ["upload"]}, "description": "Upload a single image file for OCR processing"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"File upload successful\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required A2A fields\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('task_id');", "    pm.expect(jsonData).to.have.property('context_id');", "    pm.expect(jsonData).to.have.property('user_id');", "    pm.expect(jsonData).to.have.property('timestamp');", "});", "", "pm.test(\"File processed successfully\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('uploaded_files', 1);", "    pm.expect(jsonData).to.have.property('status', 'completed');", "    pm.expect(jsonData).to.have.property('ocr_result');", "});", "", "pm.test(\"OCR processing successful\", function () {", "    const jsonData = pm.response.json();", "    if (jsonData.ocr_result && jsonData.ocr_result.response) {", "        const ocrResponse = jsonData.ocr_result.response;", "        if (typeof ocrResponse === 'string') {", "            const parsedResponse = JSON.parse(ocrResponse);", "            pm.expect(parsedResponse).to.have.property('status', 'success');", "            pm.expect(parsedResponse).to.have.property('results');", "        } else {", "            pm.expect(ocrResponse).to.have.property('status', 'success');", "        }", "    }", "});"], "type": "text/javascript"}}]}, {"name": "4. Multiple Files Upload", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": [], "description": "First image file"}, {"key": "files", "type": "file", "src": [], "description": "Second image file"}, {"key": "files", "type": "file", "src": [], "description": "Third image file (optional)"}, {"key": "user_id", "value": "test_user_batch", "type": "text", "description": "Custom user ID for batch processing"}, {"key": "description", "value": "Batch OCR processing test with multiple files", "type": "text", "description": "Description of the batch processing task"}]}, "url": {"raw": "{{upload_client_url}}/upload", "host": ["{{upload_client_url}}"], "path": ["upload"]}, "description": "Upload multiple image files for batch OCR processing"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Batch upload successful\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Multiple files processed\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.uploaded_files).to.be.above(1);", "    pm.expect(jsonData.files_info).to.be.an('array');", "    pm.expect(jsonData.files_info.length).to.equal(jsonData.uploaded_files);", "});", "", "pm.test(\"All files have unique processing\", function () {", "    const jsonData = pm.response.json();", "    if (jsonData.ocr_result && jsonData.ocr_result.response) {", "        const ocrResponse = jsonData.ocr_result.response;", "        let parsedResponse = ocrResponse;", "        if (typeof ocrResponse === 'string') {", "            parsedResponse = JSON.parse(ocrResponse);", "        }", "        if (parsedResponse.results && Array.isArray(parsedResponse.results)) {", "            const ids = parsedResponse.results.map(r => r.id);", "            const uniqueIds = [...new Set(ids)];", "            pm.expect(ids.length).to.equal(uniqueIds.length);", "        }", "    }", "});"], "type": "text/javascript"}}]}, {"name": "5. Upload Without Optional Fields", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": [], "description": "Image file without additional metadata"}]}, "url": {"raw": "{{upload_client_url}}/upload", "host": ["{{upload_client_url}}"], "path": ["upload"]}, "description": "Test upload with only required files field, no optional metadata"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Upload works without optional fields\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Auto-generated fields present\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.user_id).to.include('upload_user_');", "    pm.expect(jsonData.task_id).to.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);", "    pm.expect(jsonData.context_id).to.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);", "});"], "type": "text/javascript"}}]}, {"name": "6. <PERSON><PERSON><PERSON> Test - No Files", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "user_id", "value": "test_user_error", "type": "text", "description": "User ID without any files"}, {"key": "description", "value": "Error test - no files uploaded", "type": "text"}]}, "url": {"raw": "{{upload_client_url}}/upload", "host": ["{{upload_client_url}}"], "path": ["upload"]}, "description": "Test error handling when no files are uploaded"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Returns error for no files\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Error message is descriptive\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('error');", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData.error).to.include('No files uploaded');", "});"], "type": "text/javascript"}}]}, {"name": "7. Upload Client Info Page", "request": {"method": "GET", "header": [], "url": {"raw": "{{upload_client_url}}/", "host": ["{{upload_client_url}}"], "path": [""]}, "description": "Get the information page with usage instructions"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Info page loads\", function () {", "    pm.response.to.have.status(200);", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('text/html');", "});"], "type": "text/javascript"}}]}]}