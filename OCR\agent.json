{"capabilities": {"web_search": {"description": "Perform web searches using Firecrawl technology", "supported_operations": ["search", "crawl", "extract"]}, "content_extraction": {"description": "Extract and process content from web pages", "supported_formats": ["html", "text", "markdown"]}}, "defaultInputModes": ["text/plain"], "defaultOutputModes": ["application/json", "text/plain"], "description": "A web search agent that can perform web searches, extract content from websites, and provide comprehensive information using Firecrawl MCP technology. Specializes in real-time web content retrieval and analysis.", "name": "ocr_web_search_agent", "skills": [{"id": "web_search", "name": "Web Search", "description": "Search the web for information using advanced crawling technology", "tags": ["search", "web", "information", "research"]}, {"id": "content_extraction", "name": "Content Extraction", "description": "Extract and process content from web pages and URLs", "tags": ["extraction", "content", "web", "processing"]}, {"id": "web_crawling", "name": "Web Crawling", "description": "Crawl websites to gather structured information", "tags": ["crawling", "web", "data", "collection"]}], "url": "http://localhost:8000/a2a/ocr_web_search_agent", "version": "1.0.0"}