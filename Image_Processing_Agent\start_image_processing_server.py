#!/usr/bin/env python3
"""
Startup script for the Image Processing Agent server.
Provides easy testing and deployment of the image processing agent.
"""

import sys
import os
import subprocess
import time
import requests
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_server_health(url: str, max_retries: int = 10, delay: int = 2) -> bool:
    """Check if the server is running and healthy."""
    for attempt in range(max_retries):
        try:
            response = requests.get(f"{url}/.well-known/agent.json", timeout=5)
            if response.status_code == 200:
                logger.info(f"✅ Server is healthy and responding at {url}")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if attempt < max_retries - 1:
            logger.info(f"⏳ Waiting for server to start... (attempt {attempt + 1}/{max_retries})")
            time.sleep(delay)
    
    return False


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    try:
        import google.adk
        import a2a
        import fastapi
        import uvicorn
        logger.info("✅ Core dependencies found")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.error("Please install dependencies with: pip install -r requirements.txt")
        return False


def check_api_key() -> bool:
    """Check if Google API key is available."""
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        # Check .env file
        env_file = Path(__file__).parent / ".env"
        if env_file.exists():
            from dotenv import load_dotenv
            load_dotenv(env_file)
            api_key = os.getenv("GOOGLE_API_KEY")
    
    if api_key and api_key.startswith("AIza"):
        logger.info("✅ Google API key found")
        return True
    else:
        logger.error("❌ Google API key not found or invalid")
        logger.error("Please set GOOGLE_API_KEY in environment or .env file")
        return False


def start_image_processing_server(host: str = "localhost", port: int = 8005):
    """Start the Image Processing Agent server."""
    
    # Get the current directory
    current_dir = Path(__file__).parent
    
    logger.info(f"🚀 Starting Image Processing Agent server on {host}:{port}")
    logger.info(f"📁 Working directory: {current_dir}")
    
    try:
        # Start the server process
        cmd = [sys.executable, "_main_.py", "--host", host, "--port", str(port)]
        logger.info(f"🔧 Running command: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            cwd=current_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Give the server a moment to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            logger.error(f"❌ Server process exited early")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False
        
        # Check server health
        server_url = f"http://{host}:{port}"
        if check_server_health(server_url):
            logger.info(f"🎉 Image Processing Agent server is running successfully!")
            logger.info(f"🌐 Agent card: {server_url}/.well-known/agent.json")
            logger.info(f"🔗 A2A endpoint: {server_url}/a2a/image_processing_agent")
            logger.info(f"🖼️  Ready to process images with Google Gemini vision!")
            logger.info(f"📋 Use Ctrl+C to stop the server")
            
            try:
                # Keep the server running and show logs
                while True:
                    # Read and display server output
                    output = process.stdout.readline()
                    if output:
                        print(f"[SERVER] {output.strip()}")
                    
                    # Check if process is still alive
                    if process.poll() is not None:
                        logger.warning("⚠️  Server process has stopped")
                        break
                        
                    time.sleep(0.1)
                    
            except KeyboardInterrupt:
                logger.info("🛑 Stopping server...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("⚠️  Force killing server process")
                    process.kill()
                logger.info("✅ Server stopped")
                return True
        else:
            logger.error(f"❌ Server failed to start or is not responding")
            process.terminate()
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to start server: {e}")
        return False


def main():
    """Main function."""
    print("Image Processing Agent Server Starter")
    print("=" * 40)
    print("This script starts the Image Processing Agent server for testing.")
    print("The server will run on http://localhost:8005")
    print()
    
    # Check if we're in the right directory
    current_dir = Path(__file__).parent
    main_file = current_dir / "_main_.py"
    
    if not main_file.exists():
        print(f"❌ Error: {main_file} not found!")
        print("Please run this script from the Image_Processing_Agent directory.")
        sys.exit(1)
    
    print("🔍 Running prerequisite checks...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check API key
    if not check_api_key():
        sys.exit(1)
    
    print("✅ Prerequisites check passed")
    print("🚀 Starting Image Processing Agent server...")
    print()
    
    success = start_image_processing_server()
    
    if success:
        print("\n🎉 Server session completed successfully")
    else:
        print("\n❌ Server failed to start or encountered an error")
        sys.exit(1)


if __name__ == "__main__":
    main()
