# OCR Web Search Agent

This directory contains an ADK (Agent Development Kit) agent that provides web search capabilities using Google A2A server framework and Firecrawl MCP integration.

## Features

- **Web Search**: Performs web searches using Firecrawl MCP server
- **Content Extraction**: Crawls and extracts content from web pages
- **Google Gemini Integration**: Uses Google Gemini AI models for intelligent responses
- **A2A Server**: Runs on Google A2A (Agent to Agent) server framework

## Configuration

The agent is configured with:
- **MCP Server**: Firecrawl MCP server via `npx -y firecrawl-mcp`
- **API Key**: Firecrawl API key: `fc-9c8bbdeff8d248dbaec414064b0b7a9d`
- **AI Model**: Google Gemini with API key: `AIzaSyB5jvOM9xD7fvDs8C5b4T2N-u58chACTo0`

## Structure

```
OCR/
├── README.md              # This file
├── agent.py              # Main agent implementation
├── server.py             # A2A server setup
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
└── .env.example          # Environment variables template
```

## Usage

1. Set up environment variables
2. Install dependencies: `pip install -r requirements.txt`
3. Run the server: `python server.py`
4. Access the agent via A2A protocol endpoints

## Dependencies

- google-adk (Agent Development Kit)
- a2a-sdk (A2A server framework)
- mcp (Model Context Protocol)
- google-genai (Google Gemini AI)
