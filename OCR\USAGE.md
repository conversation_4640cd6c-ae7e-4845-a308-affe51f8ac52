# OCR Web Search Agent - Usage Guide

## Overview

The OCR Web Search Agent is an ADK (Agent Development Kit) agent that provides intelligent web search capabilities using Google's A2A (Agent to Agent) server framework and Firecrawl MCP (Model Context Protocol) integration.

## Features

- **Intelligent Web Search**: Powered by Google Gemini AI models
- **Content Extraction**: Advanced web crawling and content processing via Firecrawl
- **A2A Protocol**: Compatible with Google's Agent-to-Agent communication framework
- **MCP Integration**: Uses Model Context Protocol for tool communication
- **Real-time Processing**: Live web content retrieval and analysis

## Prerequisites

1. **Python 3.10+**: Required for MCP and ADK compatibility
2. **Node.js**: Required for Firecrawl MCP server (`npx` command)
3. **API Keys**: 
   - Firecrawl API key (provided: `fc-9c8bbdeff8d248dbaec414064b0b7a9d`)
   - Google Gemini API key (provided: `AIzaSyB5jvOM9xD7fvDs8C5b4T2N-u58chACTo0`)

## Quick Start

### Option 1: Using the startup scripts

**Windows:**
```cmd
cd OCR
run.bat
```

**Linux/macOS:**
```bash
cd OCR
./run.sh
```

### Option 2: Manual setup

1. **Install dependencies:**
```bash
cd OCR
pip install -r requirements.txt
```

2. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your API keys if needed
```

3. **Start the server:**
```bash
python start.py
```

## Server Endpoints

Once running, the agent provides these endpoints:

- **Agent Card**: `http://localhost:8000/.well-known/agent.json`
- **A2A RPC**: `http://localhost:8000/a2a/ocr_web_search_agent`
- **Health Check**: `http://localhost:8000/health`
- **Root Info**: `http://localhost:8000/`

## Usage Examples

### 1. Web Search Query
Send a request to search for information:
```json
{
  "query": "What are the latest developments in AI technology?"
}
```

### 2. Content Extraction
Extract content from a specific URL:
```json
{
  "action": "extract",
  "url": "https://example.com/article"
}
```

### 3. Multi-source Research
Research a topic across multiple sources:
```json
{
  "query": "Compare renewable energy trends in 2024",
  "sources": "multiple"
}
```

## Configuration

### Environment Variables

- `FIRECRAWL_API_KEY`: API key for Firecrawl service
- `GOOGLE_API_KEY`: API key for Google Gemini models
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `LOG_LEVEL`: Logging level (default: INFO)

### Agent Configuration

The agent is configured with:
- **Model**: `gemini-2.0-flash` (configurable in `config.py`)
- **Temperature**: 0.1 (for consistent responses)
- **Safety Settings**: Medium and above blocking for harmful content

## Architecture

```
OCR Web Search Agent
├── FastAPI Server (A2A Protocol)
├── Google Gemini LLM
├── Firecrawl MCP Toolset
│   ├── Web Search Tools
│   ├── Content Extraction Tools
│   └── URL Processing Tools
└── A2A Agent Executor
```

## Troubleshooting

### Common Issues

1. **MCP Connection Errors**
   - Ensure Node.js and npx are installed
   - Check Firecrawl API key validity
   - Verify network connectivity

2. **Gemini API Errors**
   - Verify Google API key is valid
   - Check API quota and billing
   - Ensure proper authentication

3. **Import Errors**
   - Ensure Python 3.10+ is being used
   - Install all dependencies: `pip install -r requirements.txt`
   - Check virtual environment activation

### Logs

Check the log file `ocr_agent.log` for detailed error information.

## Development

### File Structure
```
OCR/
├── agent.py              # Main agent implementation
├── config.py             # Configuration settings
├── mcp_integration.py    # Firecrawl MCP setup
├── server.py             # A2A server implementation
├── start.py              # Startup script
├── agent.json            # Agent card configuration
├── requirements.txt      # Dependencies
├── .env                  # Environment variables
└── run.sh/run.bat        # Platform-specific startup scripts
```

### Extending the Agent

To add new capabilities:

1. **Add new tools** in `mcp_integration.py`
2. **Update agent instructions** in `agent.py`
3. **Modify agent card** in `agent.json`
4. **Update configuration** in `config.py`

## Support

For issues or questions:
1. Check the logs for error details
2. Verify all prerequisites are met
3. Ensure API keys are valid and have proper permissions
4. Check network connectivity for MCP server communication
