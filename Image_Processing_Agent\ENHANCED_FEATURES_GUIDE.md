# Enhanced Image Processing Agent - Video Frame Analysis & Cursor Tracking

This guide covers the enhanced features for video frame analysis, mouse cursor tracking, and URL extraction using both Gemini 2.0 Flash and OpenAI GPT-4o-mini models.

## 🎯 **Enhanced Features Overview**

### 1. **Mouse Cursor Detection & Tracking**
- **Template Matching**: Uses OpenCV template matching to detect cursor positions
- **Coordinate Extraction**: Provides exact pixel coordinates (x, y) for cursor positions
- **Cursor Type Recognition**: Identifies different cursor types (arrow, hand, text, loading, etc.)
- **Movement Analysis**: Tracks cursor movement patterns across video frames
- **Memory Integration**: Stores cursor history in Google ADK memory service

### 2. **Browser URL Extraction**
- **Address Bar Detection**: Extracts URLs from browser address bars
- **Protocol Identification**: Detects HTTP vs HTTPS
- **Domain Parsing**: Separates domain, path, and query parameters
- **Page Title Extraction**: Captures visible page titles

### 3. **UI Element Tracking**
- **Interactive Elements**: Identifies buttons, links, form fields
- **Element Coordinates**: Provides position and size information
- **State Detection**: Recognizes hover, focus, disabled states
- **Click Target Analysis**: Maps clickable areas

### 4. **Video Frame Temporal Analysis**
- **Frame Sequencing**: Analyzes frames in temporal context
- **Activity Detection**: Identifies loading states, animations, transitions
- **Form Interactions**: Tracks data entry and form submissions
- **Screen Changes**: Detects content updates between frames

## 🚀 **Getting Started**

### Prerequisites

1. **Install Enhanced Dependencies**:
```bash
pip install opencv-python numpy
```

2. **Configure API Keys**:
```bash
# For Gemini (default)
export GOOGLE_API_KEY="your-gemini-api-key"

# For OpenAI GPT-4o-mini (alternative)
export OPENAI_API_KEY="your-openai-api-key"
export VISION_MODEL="openai"
```

### Starting the Enhanced Server

#### Option 1: Gemini 2.0 Flash (Default)
```bash
python start_enhanced_server.py
```

#### Option 2: OpenAI GPT-4o-mini
```bash
# Set model preference
export VISION_MODEL="openai"
python start_enhanced_server.py
```

## 📊 **Enhanced Response Format**

When processing video frames, you'll receive enhanced responses with additional fields:

```json
{
  "task_id": "...",
  "uploaded_files": 8,
  "resultData": [
    {
      "id": "frame-001",
      "original_name": "video_frame_0001",
      "content_description": "Browser login page with cursor visible",
      "extracted_text": "Login to Salesforce...",
      
      // Enhanced cursor tracking
      "mouse_cursor": {
        "visible": true,
        "coordinates": {"x": 245, "y": 156},
        "cursor_type": "arrow",
        "size": {"width": 15, "height": 20},
        "state": "normal"
      },
      
      // Browser information extraction
      "browser_info": {
        "url": "https://login.salesforce.com",
        "domain": "login.salesforce.com",
        "protocol": "https",
        "path": "/",
        "title": "Salesforce Login"
      },
      
      // UI elements with coordinates
      "ui_elements": [
        {
          "type": "button",
          "text": "Log In",
          "coordinates": {"x": 200, "y": 300},
          "size": {"width": 80, "height": 35},
          "state": "normal"
        },
        {
          "type": "input",
          "text": "Username",
          "coordinates": {"x": 150, "y": 200},
          "size": {"width": 200, "height": 30},
          "state": "focus"
        }
      ],
      
      // Screen activity detection
      "screen_activity": {
        "loading_indicators": [],
        "animations": ["fade-in transition"],
        "notifications": [],
        "form_interactions": ["username field focused"]
      },
      
      // Video frame metadata
      "video_timestamp": 1.0284820516431925,
      "frame_number": 1,
      "source_video": "salesforce_login.mp4"
    }
  ],
  
  // Cursor movement analysis across all frames
  "cursor_movement_analysis": {
    "status": "success",
    "total_positions": 8,
    "total_distance": 245.7,
    "average_velocity": 35.2,
    "max_velocity": 67.8,
    "movements": [
      {
        "from_frame": 0,
        "to_frame": 1,
        "from_position": {"x": 100, "y": 150},
        "to_position": {"x": 245, "y": 156},
        "distance": 145.2,
        "time_diff": 1.028,
        "velocity": 141.2,
        "direction": {"dx": 145, "dy": 6}
      }
    ],
    "cursor_types_detected": ["arrow", "hand"],
    "time_span": {
      "start": 0.0,
      "end": 7.0,
      "duration": 7.0
    }
  },
  
  "cursor_tracking_summary": {
    "total_positions_tracked": 8,
    "total_movement_distance": 245.7,
    "average_velocity": 35.2,
    "cursor_types_detected": ["arrow", "hand"],
    "tracking_duration": 7.0
  }
}
```

## 🔧 **Configuration Options**

### Environment Variables

```bash
# Model Selection
VISION_MODEL=gemini          # or "openai"

# Cursor Tracking
ENABLE_CURSOR_TRACKING=true
CURSOR_DETECTION_THRESHOLD=0.7
TEMPLATE_MATCHING_METHOD=TM_CCOEFF_NORMED

# Model-specific settings
GEMINI_MODEL=gemini-2.0-flash-exp
OPENAI_MODEL=gpt-4o-mini
```

### Cursor Detection Tuning

- **Threshold**: Adjust `CURSOR_DETECTION_THRESHOLD` (0.0-1.0) for sensitivity
- **Templates**: Add custom cursor templates in `cursor_tracking_service.py`
- **Matching Method**: Choose OpenCV template matching method

## 🎯 **Use Cases**

### 1. **Screen Recording Analysis**
- Analyze user interactions in software demos
- Track cursor movements for UX research
- Extract workflow patterns from recordings

### 2. **Browser Automation Testing**
- Verify correct page navigation
- Track form interactions and submissions
- Monitor loading states and transitions

### 3. **Tutorial Video Processing**
- Extract step-by-step instructions
- Identify clickable elements and their locations
- Generate automated interaction scripts

### 4. **Accessibility Analysis**
- Track cursor visibility and contrast
- Identify interactive elements for screen readers
- Analyze navigation patterns

## 🔍 **Template Matching Details**

### How Cursor Detection Works

1. **Template Creation**: Pre-defined cursor templates for common types
2. **Frame Processing**: Each video frame is analyzed with OpenCV
3. **Template Matching**: Uses `cv2.matchTemplate()` with correlation coefficient
4. **Confidence Scoring**: Matches above threshold are considered valid
5. **Coordinate Calculation**: Center point of matched template region

### Improving Detection Accuracy

1. **Add Custom Templates**: Create templates for specific cursor themes
2. **Adjust Thresholds**: Fine-tune detection sensitivity
3. **Multi-scale Matching**: Handle different cursor sizes
4. **Color Space Conversion**: Try different color spaces for better matching

## 🧠 **Memory Integration**

### ADK Memory Usage

- **Cursor History**: Stores all detected cursor positions
- **Session Persistence**: Maintains data across requests
- **Movement Analysis**: Calculates velocities and patterns
- **Template Cache**: Stores cursor templates for reuse

### Memory Keys

- `cursor_history_{session_id}`: Cursor position history
- `cursor_templates_{session_id}`: Custom cursor templates
- `frame_analysis_{session_id}`: Frame-by-frame analysis results

## 🚨 **Troubleshooting**

### Common Issues

1. **Cursor Not Detected**
   - Lower the detection threshold
   - Check cursor template quality
   - Verify frame image quality

2. **URL Extraction Fails**
   - Ensure address bar is visible
   - Check text extraction quality
   - Verify browser UI patterns

3. **Performance Issues**
   - Reduce frame resolution
   - Limit template matching regions
   - Optimize cursor templates

### Debug Mode

Enable detailed logging:
```bash
export LOG_LEVEL=DEBUG
python start_enhanced_server.py
```

## 📈 **Performance Optimization**

### For Large Videos

1. **Frame Sampling**: Process every Nth frame instead of all frames
2. **Region of Interest**: Limit cursor detection to specific screen areas
3. **Template Optimization**: Use smaller, more efficient cursor templates
4. **Batch Processing**: Process multiple frames in parallel

### Memory Management

1. **History Cleanup**: Periodically clear old cursor history
2. **Template Caching**: Reuse templates across sessions
3. **Selective Storage**: Store only significant cursor movements

This enhanced system provides comprehensive video frame analysis with precise cursor tracking and UI element detection, making it ideal for screen recording analysis, automation testing, and user interaction research.
