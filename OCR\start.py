#!/usr/bin/env python3
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Startup script for the OCR Web Search Agent."""

import argparse
import logging
import sys
from pathlib import Path

# Add the parent directory to the Python path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from server import run_server
from config import config


def setup_logging(log_level: str = "INFO"):
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('ocr_agent.log')
        ]
    )


def main():
    """Main entry point for the OCR Web Search Agent."""
    parser = argparse.ArgumentParser(
        description="OCR Web Search Agent A2A Server"
    )
    parser.add_argument(
        "--host",
        default=config.HOST,
        help=f"Host to bind the server (default: {config.HOST})"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=config.PORT,
        help=f"Port to bind the server (default: {config.PORT})"
    )
    parser.add_argument(
        "--log-level",
        default=config.LOG_LEVEL,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help=f"Logging level (default: {config.LOG_LEVEL})"
    )
    
    args = parser.parse_args()
    
    # Update config with command line arguments
    config.HOST = args.host
    config.PORT = args.port
    config.LOG_LEVEL = args.log_level
    
    # Setup logging
    setup_logging(args.log_level)
    
    logger = logging.getLogger(__name__)
    logger.info("Starting OCR Web Search Agent...")
    logger.info(f"Configuration: Host={config.HOST}, Port={config.PORT}")
    logger.info(f"Firecrawl API Key: {'***' + config.FIRECRAWL_API_KEY[-4:] if config.FIRECRAWL_API_KEY else 'Not set'}")
    logger.info(f"Google API Key: {'***' + config.GOOGLE_API_KEY[-4:] if config.GOOGLE_API_KEY else 'Not set'}")
    
    try:
        # Run the server
        run_server()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
