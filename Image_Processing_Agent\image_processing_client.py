"""
A2A Image Processing Client - FastAPI server that accepts HTTP file uploads (images and videos)
and converts them to A2A protocol messages for the Image Processing Agent using the A2A SDK.
"""

import asyncio
import base64
import json
import logging
import os
import tempfile
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional
from uuid import uuid4

import cv2
import httpx
import uvicorn
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from pydantic import BaseModel
import shutil

# A2A SDK imports
from a2a.client import A2AClient, create_text_message_object
from a2a.types import (
    MessageSendConfiguration,
    MessageSendParams,
    SendMessageRequest,
    SendMessageResponse,
    SendMessageSuccessResponse,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supported image formats
SUPPORTED_IMAGE_FORMATS = {
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.bmp': 'image/bmp',
    '.tiff': 'image/tiff',
    '.tif': 'image/tiff'
}

# Supported video formats
SUPPORTED_VIDEO_FORMATS = {
    '.mp4': 'video/mp4',
    '.avi': 'video/x-msvideo',
    '.mov': 'video/quicktime',
    '.mkv': 'video/x-matroska',
    '.webm': 'video/webm',
    '.wmv': 'video/x-ms-wmv',
    '.flv': 'video/x-flv',
    '.m4v': 'video/x-m4v'
}

# Maximum file size (50MB)
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB in bytes

# Response models
class ImageAnalysisResult(BaseModel):
    id: str
    original_name: str
    updated_name: str
    content_description: str
    extracted_text: str
    visual_elements: Dict[str, Any]
    context_analysis: str
    quality_assessment: Dict[str, Any]
    suggested_tags: List[str]
    confidence_score: float
    timestamp: Optional[float] = None  # Video frame timestamp

class ImageProcessingResponse(BaseModel):
    task_id: str
    context_id: str
    user_id: str
    timestamp: int
    uploaded_files: int
    files_info: List[Dict[str, Any]]
    resultData: List[Dict[str, Any]]  # <-- Top-level result array
    status: str
    video_info: Optional[Dict[str, Any]] = None  # Video metadata


class VideoFrameExtractor:
    """Utility class for extracting frames from video files."""
    
    @staticmethod
    def extract_frames(video_path: str, fps_rate: float = 0.2) -> List[Dict[str, Any]]:
        """
        Extract frames from video at specified FPS rate - robust version.
        """
        frames_data = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"Cannot open video file: {video_path}")
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0
            
            print(f"Video info: FPS={fps}, Duration={duration:.2f}s, Total frames={total_frames}")
            
            # Calculate time interval between frames to extract
            time_interval = 1.0 / fps_rate  # seconds between extractions
            
            frame_id = 0
            next_extraction_time = 0.0
            
            # Read frame by frame
            frame_count = 0
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                current_time = frame_count / fps
                
                # Check if we should extract this frame
                if current_time >= next_extraction_time:
                    # Convert frame to JPEG format in memory
                    success, buffer = cv2.imencode('.jpg', frame)
                    if success:
                        frame_data = {
                            'id': str(uuid4()),
                            'frame_number': frame_id,
                            'timestamp': current_time,
                            'data': buffer.tobytes(),
                            'size': len(buffer.tobytes())
                        }
                        frames_data.append(frame_data)
                        frame_id += 1
                        
                        # Set next extraction time
                        next_extraction_time += time_interval
                        
                        print(f"Extracted frame {frame_id} at {current_time:.2f}s")
                
                frame_count += 1
            
            cap.release()
            
            video_info = {
                'fps': fps,
                'duration': duration,
                'total_frames': total_frames,
                'extracted_frames': len(frames_data)
            }
            
            print(f"✅ Extracted {len(frames_data)} frames at {fps_rate} fps")
            return frames_data, video_info
            
        except Exception as e:
            print(f"❌ Error: {e}")
            raise ValueError(f"Failed to extract frames: {str(e)}")

class A2AImageProcessingClient:
    """A2A client that handles file uploads (images and videos) and forwards them to Image Processing Agent using A2A SDK."""

    def __init__(self, agent_url: str = "http://localhost:8005", client_port: int = 8006):
        self.agent_url = agent_url
        self.client_port = client_port
        self.httpx_client: Optional[httpx.AsyncClient] = None
        self.a2a_client: Optional[A2AClient] = None
        self.video_extractor = VideoFrameExtractor()

        self.app = FastAPI(
            title="A2A Image/Video Processing Client",
            description="HTTP API client for image and video processing using A2A SDK",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )

        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        self.setup_routes()

    async def connect_to_agent(self) -> None:
        """Connect to the Image Processing Agent using A2A SDK."""
        try:
            logger.info(f"Connecting to Image Processing Agent at {self.agent_url}")

            # Create HTTP client
            self.httpx_client = httpx.AsyncClient(timeout=300.0)  # 5 minute timeout for video processing

            # The Image Processing Agent uses RPC endpoint at /a2a/image_processing_agent
            rpc_endpoint_url = f"{self.agent_url}/a2a/image_processing_agent"

            # Create A2A client with direct RPC endpoint URL
            self.a2a_client = A2AClient(
                httpx_client=self.httpx_client,
                url=rpc_endpoint_url
            )

            logger.info(f"Successfully connected to Image Processing Agent RPC endpoint: {rpc_endpoint_url}")

        except Exception as e:
            logger.error(f"Failed to connect to Image Processing Agent: {e}")
            if self.httpx_client:
                await self.httpx_client.aclose()
            raise

    async def disconnect_from_agent(self) -> None:
        """Disconnect from the Image Processing Agent."""
        if self.httpx_client:
            await self.httpx_client.aclose()
            self.httpx_client = None
            self.a2a_client = None
            logger.info("Disconnected from Image Processing Agent")
        
    def setup_routes(self):
        """Setup HTTP routes for the image/video processing client."""
        
        @self.app.get("/")
        async def root():
            """Root endpoint with API information."""
            return HTMLResponse(content="""
            <html>
                <head>
                    <title>A2A Image/Video Processing Client</title>
                </head>
                <body>
                    <h1>A2A Image/Video Processing Client</h1>
                    <p>Upload images or videos for AI-powered analysis</p>
                    <h2>Supported Formats:</h2>
                    <h3>Images:</h3>
                    <ul>
                        <li>PNG, JPG, JPEG, GIF, WebP, BMP, TIFF</li>
                    </ul>
                    <h3>Videos:</h3>
                    <ul>
                        <li>MP4, AVI, MOV, MKV, WebM, WMV, FLV, M4V</li>
                        <li>Maximum size: 50MB</li>
                        <li>Videos are processed at 1 frame per second</li>
                    </ul>
                    <p><a href="/docs">API Documentation</a></p>
                </body>
            </html>
            """)

        @self.app.post("/upload", response_model=ImageProcessingResponse)
        async def upload_files(
            files: List[UploadFile] = File(..., description="Image or video files to process"),
            user_id: Optional[str] = Form(None, description="Optional user ID"),
            description: Optional[str] = Form(None, description="Optional description for the processing task"),
            fps_rate: Optional[float] = Form(0.2, description="Frames per second to extract from videos (default: 1)")
        ):
            """
            Upload and process multiple image or video files.
            
            This endpoint accepts multiple image/video files via multipart/form-data and forwards them
            to the A2A Image Processing Agent for comprehensive analysis.
            
            For videos, frames are extracted at the specified FPS rate (default: 1 frame per second).
            """
            return await self.handle_file_upload(files, user_id, description, fps_rate)

    async def handle_file_upload(
        self,
        files: List[UploadFile],
        user_id: Optional[str] = None,
        description: Optional[str] = None,
        fps_rate: int = 1
    ) -> ImageProcessingResponse:
        """Handle multipart file upload and forward to Image Processing Agent using A2A SDK."""
        temp_dir = None
        try:
            # Ensure A2A client is connected
            if not self.a2a_client:
                await self.connect_to_agent()

            # Generate A2A protocol fields automatically
            task_id = str(uuid4())
            context_id = str(uuid4())
            timestamp = int(time.time() * 1000)

            if not user_id:
                user_id = f"media_user_{int(time.time())}"

            if not description:
                description = "Image and video processing and analysis"

            logger.info(f"Processing file upload request - Task ID: {task_id}")
            logger.info(f"Received {len(files)} files for processing")
            
            # Create temporary directory for video processing
            temp_dir = tempfile.mkdtemp(prefix="video_processing_")
            
            # Validate and process files
            processed_files = []
            video_info = None
            total_original_files = 0
            
            for i, file in enumerate(files):
                if not file.filename:
                    raise HTTPException(status_code=400, detail=f"File {i+1} has no filename")
                
                # Check file extension
                file_ext = Path(file.filename).suffix.lower()
                
                # Read file content
                file_content = await file.read()
                if len(file_content) == 0:
                    raise HTTPException(status_code=400, detail=f"File {file.filename} is empty")
                
                # Check file size
                if len(file_content) > MAX_FILE_SIZE:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"File {file.filename} exceeds maximum size of {MAX_FILE_SIZE / (1024*1024):.1f}MB"
                    )
                
                if file_ext in SUPPORTED_IMAGE_FORMATS:
                    # Process as image
                    base64_data = base64.b64encode(file_content).decode('utf-8')
                    
                    processed_file = {
                        "id": str(uuid.uuid4()),
                        "name": Path(file.filename).stem,
                        "original_filename": file.filename,
                        "data": base64_data,
                        "mime_type": SUPPORTED_IMAGE_FORMATS[file_ext],
                        "size": len(file_content),
                        "description": f"Image {i+1} for processing",
                        "type": "image"
                    }
                    
                    processed_files.append(processed_file)
                    total_original_files += 1
                    logger.debug(f"Processed image {i+1}: {file.filename} ({len(file_content)} bytes)")
                    
                elif file_ext in SUPPORTED_VIDEO_FORMATS:
                    # Process as video
                    logger.info(f"Processing video file: {file.filename}")
                    
                    # Save video to temporary file
                    video_path = os.path.join(temp_dir, f"video_{i}_{file.filename}")
                    with open(video_path, "wb") as f:
                        f.write(file_content)
                    
                    # Extract frames
                    try:
                        frames_data, vid_info = self.video_extractor.extract_frames(video_path, fps_rate)
                        video_info = vid_info
                        
                        # Convert frames to processed files
                        for frame_idx, frame_data in enumerate(frames_data):
                            base64_data = base64.b64encode(frame_data['data']).decode('utf-8')
                            
                            processed_file = {
                                "id": frame_data['id'],
                                "name": f"{Path(file.filename).stem}_frame_{frame_data['frame_number']:04d}",
                                "original_filename": f"{Path(file.filename).stem}_frame_{frame_data['frame_number']:04d}.jpg",
                                "data": base64_data,
                                "mime_type": "image/jpeg",
                                "size": frame_data['size'],
                                "description": f"Frame {frame_data['frame_number']} from video {file.filename}",
                                "type": "video_frame",
                                "video_timestamp": frame_data['timestamp'],
                                "frame_number": frame_data['frame_number'],
                                "source_video": file.filename
                            }
                            
                            processed_files.append(processed_file)
                        
                        total_original_files += 1
                        logger.info(f"Processed video {file.filename}: extracted {len(frames_data)} frames")
                        
                    except Exception as e:
                        logger.error(f"Error processing video {file.filename}: {e}")
                        raise HTTPException(
                            status_code=400, 
                            detail=f"Error processing video {file.filename}: {str(e)}"
                        )
                else:
                    # Unsupported format
                    supported_formats = list(SUPPORTED_IMAGE_FORMATS.keys()) + list(SUPPORTED_VIDEO_FORMATS.keys())
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Unsupported file format: {file_ext}. Supported formats: {', '.join(supported_formats)}"
                    )
            
            logger.info(f"Successfully processed {total_original_files} files into {len(processed_files)} items for analysis")

            # Forward to Image Processing Agent using A2A SDK
            processing_result = await self.forward_to_image_agent_a2a(
                files=processed_files,
                context_id=context_id,
                description=description
            )
     
            # Extract image results for top-level resultData and add timestamps
            result_data = []
            if (processing_result.get('status') == 'success' and
                'results' in processing_result):
                results = processing_result['results']
                
                # Add video timestamps to results
                for result in results:
                    # Find corresponding processed file to get timestamp
                    matching_file = None
                    for pf in processed_files:
                        if (pf.get('name') == result.get('original_name') or 
                            pf.get('original_filename') == result.get('original_name')):
                            matching_file = pf
                            break
                    
                    if matching_file and matching_file.get('type') == 'video_frame':
                        result['video_timestamp'] = matching_file.get('video_timestamp')
                        result['frame_number'] = matching_file.get('frame_number')
                        result['source_video'] = matching_file.get('source_video')
                    
                result_data = results
            elif 'content' in processing_result and isinstance(processing_result['content'], list):
                result_data = processing_result['content']

            # Prepare files info for response
            files_info = []
            for f in processed_files:
                file_info = {
                    "id": f["id"],
                    "name": f["name"],
                    "original_filename": f["original_filename"],
                    "size": f["size"],
                    "mime_type": f["mime_type"],
                    "type": f["type"]
                }
                
                # Add video-specific info
                if f["type"] == "video_frame":
                    file_info.update({
                        "video_timestamp": f["video_timestamp"],
                        "frame_number": f["frame_number"],
                        "source_video": f["source_video"]
                    })
                
                files_info.append(file_info)

            # Return response with A2A metadata and resultData
            response = ImageProcessingResponse(
                task_id=task_id,
                context_id=context_id,
                user_id=user_id,
                timestamp=timestamp,
                uploaded_files=len(processed_files),
                files_info=files_info,
                resultData=result_data,
                status="completed",
                video_info=video_info
            )

            return response

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing file upload: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
        finally:
            # Clean up temporary directory
            if temp_dir and os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    logger.debug(f"Cleaned up temporary directory: {temp_dir}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary directory {temp_dir}: {e}")
    
    async def forward_to_image_agent_a2a(
        self,
        files: List[Dict],
        context_id: str,
        description: str
    ) -> Dict[str, Any]:
        """Forward the processed files to the Image Processing Agent using A2A SDK."""
        try:
            if not self.a2a_client:
                raise ValueError("A2A client not connected. Call connect_to_agent() first.")

            # Prepare image data for the agent
            image_data = {
                "images": files,
                "message": description
            }

            # Create the message content as JSON string
            message_content = json.dumps(image_data)

            logger.info(f"Sending {len(files)} items for processing via A2A SDK")
            logger.debug(f"Message content preview: {message_content[:200]}...")

            # Create the A2A message using the helper function
            message = create_text_message_object(content=message_content)
            if context_id:
                message.contextId = context_id

            # Create message send parameters
            params = MessageSendParams(
                message=message,
                configuration=MessageSendConfiguration(
                    acceptedOutputModes=["text", "application/json"],
                    blocking=True  # Wait for complete response
                )
            )

            # Create and send the request
            request = SendMessageRequest(
                id=str(uuid4()),
                params=params
            )

            logger.info("Sending A2A request to Image Processing Agent...")
            response: SendMessageResponse = await self.a2a_client.send_message(request)

            # Process the A2A response
            return self._process_a2a_response(response)

        except Exception as e:
            logger.error(f"Error forwarding to Image Processing Agent via A2A: {e}", exc_info=True)
            return {
                "error": f"Failed to communicate with Image Processing Agent: {str(e)}",
                "agent_url": self.agent_url,
                "error_type": type(e).__name__
            }

    def _process_a2a_response(self, response: SendMessageResponse) -> Dict[str, Any]:
        """Process the A2A response from the Image Processing Agent."""
        try:
            if isinstance(response.root, SendMessageSuccessResponse):
                result = response.root.result
                response_text = None

                if hasattr(result, 'parts') and result.parts:
                    for part in result.parts:
                        if hasattr(part, 'root') and hasattr(part.root, 'text'):
                            response_text = part.root.text
                            break
                elif hasattr(result, 'text'):
                    response_text = result.text

                # Try to parse as JSON
                if response_text:
                    try:
                        parsed_json = json.loads(response_text)
                        if isinstance(parsed_json, dict) and 'results' in parsed_json:
                            return {
                                'status': 'success',
                                'results': parsed_json['results']
                            }
                    except json.JSONDecodeError:
                        # Try to extract JSON from markdown code block
                        import re
                        json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_text, re.DOTALL)
                        if json_match:
                            try:
                                json_content = json_match.group(1)
                                parsed_json = json.loads(json_content)
                                if isinstance(parsed_json, dict) and 'results' in parsed_json:
                                    return {
                                        'status': 'success',
                                        'results': parsed_json['results']
                                    }
                            except json.JSONDecodeError:
                                pass
                # Fallback: return empty results
                return {
                    'status': 'success',
                    'results': []
                }
            else:
                return {
                    'status': 'error',
                    'error': str(response.root),
                    'error_details': response.root.model_dump() if hasattr(response.root, 'model_dump') else None
                }
        except Exception as e:
            logger.error(f"Error processing A2A response: {e}", exc_info=True)
            return {
                'status': 'error',
                'error': f"Failed to process A2A response: {str(e)}",
                'error_type': type(e).__name__
            }

    async def startup_event(self):
        """FastAPI startup event to initialize A2A client."""
        try:
            await self.connect_to_agent()
            logger.info("A2A client connected successfully during startup")
        except Exception as e:
            logger.warning(f"Failed to connect A2A client during startup: {e}")
            logger.info("A2A client will attempt to connect on first request")

    async def shutdown_event(self):
        """FastAPI shutdown event to cleanup A2A client."""
        await self.disconnect_from_agent()
        logger.info("A2A client disconnected during shutdown")

    def run(self, host: str = "localhost", port: int = None):
        """Run the FastAPI server with A2A client lifecycle management."""
        if port is None:
            port = self.client_port

        # Add startup and shutdown events
        self.app.add_event_handler("startup", self.startup_event)
        self.app.add_event_handler("shutdown", self.shutdown_event)

        logger.info(f"Starting A2A Image/Video Processing Client on {host}:{port}")
        logger.info(f"Image Processing Agent URL: {self.agent_url}")
        logger.info(f"API Documentation: http://{host}:{port}/docs")
        logger.info("Using A2A SDK for agent communication")
        logger.info("Supported formats: Images (PNG, JPG, etc.) and Videos (MP4, AVI, etc.)")
        logger.info("Maximum file size: 50MB")

        uvicorn.run(self.app, host=host, port=port)


def create_app(agent_url: str = "http://localhost:8005") -> FastAPI:
    """Create and configure the FastAPI application with A2A client."""
    client = A2AImageProcessingClient(agent_url=agent_url)

    # Add startup and shutdown events for A2A client lifecycle
    client.app.add_event_handler("startup", client.startup_event)
    client.app.add_event_handler("shutdown", client.shutdown_event)

    return client.app


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="A2A Image/Video Processing Client using A2A SDK")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8006, help="Port to bind to")
    parser.add_argument("--agent-url", default="http://localhost:8005", help="Image Processing Agent URL")

    args = parser.parse_args()

    client = A2AImageProcessingClient(agent_url=args.agent_url, client_port=args.port)
    client.run(host=args.host, port=args.port)