"""
A2A Image Processing Client - FastAPI server that accepts HTTP file uploads
and converts them to A2A protocol messages for the Image Processing Agent.
"""

import asyncio
import base64
import json
import logging
import os
import tempfile
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional

import aiohttp
import uvicorn
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supported image formats
SUPPORTED_IMAGE_FORMATS = {
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.bmp': 'image/bmp',
    '.tiff': 'image/tiff',
    '.tif': 'image/tiff'
}

# Response models
class ImageProcessingResponse(BaseModel):
    task_id: str
    context_id: str
    user_id: str
    timestamp: int
    uploaded_files: int
    files_info: List[Dict[str, Any]]
    processing_result: Dict[str, Any]
    status: str


class A2AImageProcessingClient:
    """A2A client that handles file uploads and forwards to Image Processing Agent."""
    
    def __init__(self, agent_url: str = "http://localhost:8005", client_port: int = 8006):
        self.agent_url = agent_url
        self.client_port = client_port
        self.app = FastAPI(
            title="A2A Image Processing Client",
            description="HTTP API client for image processing using A2A protocol",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self.setup_routes()
        
    def setup_routes(self):
        """Setup HTTP routes for the image processing client."""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def info_page():
            """Information page with usage instructions."""
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>A2A Image Processing Client</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    .container {{ max-width: 800px; }}
                    .endpoint {{ background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                    .method {{ color: #007acc; font-weight: bold; }}
                    .code {{ background: #f0f0f0; padding: 5px; border-radius: 3px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>A2A Image Processing Client</h1>
                    <p>This service accepts image file uploads and forwards them to the Image Processing Agent using A2A protocol.</p>
                    
                    <h2>Available Endpoints</h2>
                    
                    <div class="endpoint">
                        <span class="method">POST</span> <strong>/upload</strong><br>
                        Upload multiple image files for processing<br>
                        <em>Content-Type: multipart/form-data</em>
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">GET</span> <strong>/health</strong><br>
                        Health check endpoint
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">GET</span> <strong>/docs</strong><br>
                        Interactive API documentation (Swagger UI)
                    </div>
                    
                    <h2>Usage with Postman</h2>
                    <ol>
                        <li>Set method to <span class="code">POST</span></li>
                        <li>Set URL to <span class="code">http://localhost:{self.client_port}/upload</span></li>
                        <li>Go to Body tab → form-data</li>
                        <li>Add key <span class="code">files</span> with type <span class="code">File</span></li>
                        <li>Select multiple image files</li>
                        <li>Optionally add text fields: <span class="code">user_id</span>, <span class="code">description</span></li>
                        <li>Send the request</li>
                    </ol>
                    
                    <h2>Supported Formats</h2>
                    <p>PNG, JPEG, GIF, WebP, BMP, TIFF</p>
                    
                    <p><strong>Image Processing Agent URL:</strong> {self.agent_url}</p>
                    <p><strong>Client Port:</strong> {self.client_port}</p>
                </div>
            </body>
            </html>
            """
            return html_content
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {
                "status": "healthy",
                "service": "A2A Image Processing Client",
                "agent_url": self.agent_url,
                "timestamp": int(time.time())
            }
        
        @self.app.post("/upload", response_model=ImageProcessingResponse)
        async def upload_files(
            files: List[UploadFile] = File(..., description="Image files to process"),
            user_id: Optional[str] = Form(None, description="Optional user ID"),
            description: Optional[str] = Form(None, description="Optional description for the processing task")
        ):
            """
            Upload and process multiple image files.
            
            This endpoint accepts multiple image files via multipart/form-data and forwards them
            to the A2A Image Processing Agent for comprehensive analysis.
            """
            return await self.handle_file_upload(files, user_id, description)

    async def handle_file_upload(
        self, 
        files: List[UploadFile], 
        user_id: Optional[str] = None, 
        description: Optional[str] = None
    ) -> ImageProcessingResponse:
        """Handle multipart file upload and forward to Image Processing Agent."""
        try:
            # Generate A2A protocol fields automatically
            task_id = str(uuid.uuid4())
            context_id = str(uuid.uuid4())
            timestamp = int(time.time() * 1000)
            
            if not user_id:
                user_id = f"image_user_{int(time.time())}"
            
            if not description:
                description = "Image processing and analysis"
            
            logger.info(f"Processing file upload request - Task ID: {task_id}")
            logger.info(f"Received {len(files)} files for processing")
            
            # Validate and process files
            processed_files = []
            temp_files = []  # Track temporary files for cleanup
            
            for i, file in enumerate(files):
                if not file.filename:
                    raise HTTPException(status_code=400, f"File {i+1} has no filename")
                
                # Check file extension
                file_ext = Path(file.filename).suffix.lower()
                if file_ext not in SUPPORTED_IMAGE_FORMATS:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Unsupported file format: {file_ext}. Supported formats: {', '.join(SUPPORTED_IMAGE_FORMATS.keys())}"
                    )
                
                # Read file content
                file_content = await file.read()
                if len(file_content) == 0:
                    raise HTTPException(status_code=400, detail=f"File {file.filename} is empty")
                
                # Convert to base64 for A2A transmission
                base64_data = base64.b64encode(file_content).decode('utf-8')
                
                # Create processed file info
                processed_file = {
                    "id": str(uuid.uuid4()),
                    "name": Path(file.filename).stem,
                    "original_filename": file.filename,
                    "data": base64_data,
                    "mime_type": SUPPORTED_IMAGE_FORMATS[file_ext],
                    "size": len(file_content),
                    "description": f"Image {i+1} for processing"
                }
                
                processed_files.append(processed_file)
                logger.debug(f"Processed file {i+1}: {file.filename} ({len(file_content)} bytes)")
            
            logger.info(f"Successfully processed {len(processed_files)} files")
            
            # Forward to Image Processing Agent
            processing_result = await self.forward_to_image_agent(
                files=processed_files,
                task_id=task_id,
                context_id=context_id,
                user_id=user_id,
                timestamp=timestamp,
                description=description
            )
            
            # Return response with A2A metadata
            response = ImageProcessingResponse(
                task_id=task_id,
                context_id=context_id,
                user_id=user_id,
                timestamp=timestamp,
                uploaded_files=len(processed_files),
                files_info=[{
                    "id": f["id"],
                    "name": f["name"], 
                    "original_filename": f["original_filename"],
                    "size": f["size"],
                    "mime_type": f["mime_type"]
                } for f in processed_files],
                processing_result=processing_result,
                status="completed"
            )
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing file upload: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    
    async def forward_to_image_agent(
        self, 
        files: List[Dict], 
        task_id: str, 
        context_id: str,
        user_id: str, 
        timestamp: int, 
        description: str
    ) -> Dict[str, Any]:
        """Forward the processed files to the Image Processing Agent using A2A JSON-RPC protocol."""
        try:
            # Prepare image data for the agent
            image_data = {
                "images": files,
                "message": description
            }
            
            # Prepare A2A JSON-RPC request payload
            a2a_payload = {
                "jsonrpc": "2.0",
                "method": "message/send",
                "params": {
                    "task_id": task_id,
                    "context_id": context_id,
                    "user_id": user_id,
                    "timestamp": timestamp,
                    "message": {
                        "messageId": str(uuid.uuid4()),
                        "role": "user",
                        "parts": [
                            {
                                "text": json.dumps(image_data)
                            }
                        ],
                        "metadata": {
                            "source": "image_processing_client",
                            "description": description,
                            "file_count": len(files)
                        }
                    }
                },
                "id": task_id
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "A2A-ImageProcessing-Client/1.0"
            }
            
            agent_endpoint = f"{self.agent_url}/a2a/image_processing_agent"
            logger.info(f"Forwarding to Image Processing Agent: {agent_endpoint}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    agent_endpoint,
                    json=a2a_payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=120)  # 2 minute timeout for image processing
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"Image Processing Agent responded successfully")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"Image Processing Agent error: {response.status} - {error_text}")
                        return {
                            "error": f"Image Processing Agent returned status {response.status}",
                            "details": error_text
                        }
                        
        except Exception as e:
            logger.error(f"Error forwarding to Image Processing Agent: {e}", exc_info=True)
            return {
                "error": f"Failed to communicate with Image Processing Agent: {str(e)}",
                "agent_url": self.agent_url
            }

    def run(self, host: str = "localhost", port: int = None):
        """Run the FastAPI server."""
        if port is None:
            port = self.client_port
            
        logger.info(f"Starting A2A Image Processing Client on {host}:{port}")
        logger.info(f"Image Processing Agent URL: {self.agent_url}")
        logger.info(f"API Documentation: http://{host}:{port}/docs")
        
        uvicorn.run(self.app, host=host, port=port)


def create_app(agent_url: str = "http://localhost:8005") -> FastAPI:
    """Create and configure the FastAPI application."""
    client = A2AImageProcessingClient(agent_url=agent_url)
    return client.app


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="A2A Image Processing Client")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8006, help="Port to bind to")
    parser.add_argument("--agent-url", default="http://localhost:8005", help="Image Processing Agent URL")
    
    args = parser.parse_args()
    
    client = A2AImageProcessingClient(agent_url=args.agent_url, client_port=args.port)
    client.run(host=args.host, port=args.port)
