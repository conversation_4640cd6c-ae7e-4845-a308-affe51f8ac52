"""
A2A Image Processing Client - FastAPI server that accepts HTTP file uploads
and converts them to A2A protocol messages for the Image Processing Agent using the A2A SDK.
"""

import asyncio
import base64
import json
import logging
import os
import tempfile
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional
from uuid import uuid4

import httpx
import uvicorn
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSO<PERSON>esponse, HTMLResponse
from pydantic import BaseModel

# A2A SDK imports
from a2a.client import A2AClient, create_text_message_object
from a2a.types import (
    MessageSendConfiguration,
    MessageSendParams,
    SendMessageRequest,
    SendMessageResponse,
    SendMessageSuccessResponse,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supported image formats
SUPPORTED_IMAGE_FORMATS = {
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.bmp': 'image/bmp',
    '.tiff': 'image/tiff',
    '.tif': 'image/tiff'
}

# Response models
class ImageProcessingResponse(BaseModel):
    task_id: str
    context_id: str
    user_id: str
    timestamp: int
    uploaded_files: int
    files_info: List[Dict[str, Any]]
    processing_result: Dict[str, Any]
    status: str


class A2AImageProcessingClient:
    """A2A client that handles file uploads and forwards to Image Processing Agent using A2A SDK."""

    def __init__(self, agent_url: str = "http://localhost:8005", client_port: int = 8006):
        self.agent_url = agent_url
        self.client_port = client_port
        self.httpx_client: Optional[httpx.AsyncClient] = None
        self.a2a_client: Optional[A2AClient] = None

        self.app = FastAPI(
            title="A2A Image Processing Client",
            description="HTTP API client for image processing using A2A SDK",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )

        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        self.setup_routes()

    async def connect_to_agent(self) -> None:
        """Connect to the Image Processing Agent using A2A SDK."""
        try:
            logger.info(f"Connecting to Image Processing Agent at {self.agent_url}")

            # Create HTTP client
            self.httpx_client = httpx.AsyncClient(timeout=120.0)  # 2 minute timeout for image processing

            # The Image Processing Agent uses RPC endpoint at /a2a/image_processing_agent
            rpc_endpoint_url = f"{self.agent_url}/a2a/image_processing_agent"

            # Create A2A client with direct RPC endpoint URL
            self.a2a_client = A2AClient(
                httpx_client=self.httpx_client,
                url=rpc_endpoint_url
            )

            logger.info(f"Successfully connected to Image Processing Agent RPC endpoint: {rpc_endpoint_url}")

        except Exception as e:
            logger.error(f"Failed to connect to Image Processing Agent: {e}")
            if self.httpx_client:
                await self.httpx_client.aclose()
            raise

    async def disconnect_from_agent(self) -> None:
        """Disconnect from the Image Processing Agent."""
        if self.httpx_client:
            await self.httpx_client.aclose()
            self.httpx_client = None
            self.a2a_client = None
            logger.info("Disconnected from Image Processing Agent")
        
    def setup_routes(self):
        """Setup HTTP routes for the image processing client."""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def info_page():
            """Information page with usage instructions."""
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>A2A Image Processing Client</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    .container {{ max-width: 800px; }}
                    .endpoint {{ background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                    .method {{ color: #007acc; font-weight: bold; }}
                    .code {{ background: #f0f0f0; padding: 5px; border-radius: 3px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>A2A Image Processing Client</h1>
                    <p>This service accepts image file uploads and forwards them to the Image Processing Agent using A2A protocol.</p>
                    
                    <h2>Available Endpoints</h2>
                    
                    <div class="endpoint">
                        <span class="method">POST</span> <strong>/upload</strong><br>
                        Upload multiple image files for processing<br>
                        <em>Content-Type: multipart/form-data</em>
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">GET</span> <strong>/health</strong><br>
                        Health check endpoint
                    </div>
                    
                    <div class="endpoint">
                        <span class="method">GET</span> <strong>/docs</strong><br>
                        Interactive API documentation (Swagger UI)
                    </div>
                    
                    <h2>Usage with Postman</h2>
                    <ol>
                        <li>Set method to <span class="code">POST</span></li>
                        <li>Set URL to <span class="code">http://localhost:{self.client_port}/upload</span></li>
                        <li>Go to Body tab → form-data</li>
                        <li>Add key <span class="code">files</span> with type <span class="code">File</span></li>
                        <li>Select multiple image files</li>
                        <li>Optionally add text fields: <span class="code">user_id</span>, <span class="code">description</span></li>
                        <li>Send the request</li>
                    </ol>
                    
                    <h2>Supported Formats</h2>
                    <p>PNG, JPEG, GIF, WebP, BMP, TIFF</p>
                    
                    <p><strong>Image Processing Agent URL:</strong> {self.agent_url}</p>
                    <p><strong>Client Port:</strong> {self.client_port}</p>
                </div>
            </body>
            </html>
            """
            return html_content
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            # Check if A2A client is connected
            a2a_status = "connected" if self.a2a_client else "disconnected"

            return {
                "status": "healthy",
                "service": "A2A Image Processing Client",
                "agent_url": self.agent_url,
                "a2a_connection": a2a_status,
                "timestamp": int(time.time())
            }
        
        @self.app.post("/upload", response_model=ImageProcessingResponse)
        async def upload_files(
            files: List[UploadFile] = File(..., description="Image files to process"),
            user_id: Optional[str] = Form(None, description="Optional user ID"),
            description: Optional[str] = Form(None, description="Optional description for the processing task")
        ):
            """
            Upload and process multiple image files.
            
            This endpoint accepts multiple image files via multipart/form-data and forwards them
            to the A2A Image Processing Agent for comprehensive analysis.
            """
            return await self.handle_file_upload(files, user_id, description)

    async def handle_file_upload(
        self,
        files: List[UploadFile],
        user_id: Optional[str] = None,
        description: Optional[str] = None
    ) -> ImageProcessingResponse:
        """Handle multipart file upload and forward to Image Processing Agent using A2A SDK."""
        try:
            # Ensure A2A client is connected
            if not self.a2a_client:
                await self.connect_to_agent()

            # Generate A2A protocol fields automatically
            task_id = str(uuid4())
            context_id = str(uuid4())
            timestamp = int(time.time() * 1000)

            if not user_id:
                user_id = f"image_user_{int(time.time())}"

            if not description:
                description = "Image processing and analysis"

            logger.info(f"Processing file upload request - Task ID: {task_id}")
            logger.info(f"Received {len(files)} files for processing")
            
            # Validate and process files
            processed_files = []
            temp_files = []  # Track temporary files for cleanup
            
            for i, file in enumerate(files):
                if not file.filename:
                    raise HTTPException(status_code=400, detail=f"File {i+1} has no filename")
                
                # Check file extension
                file_ext = Path(file.filename).suffix.lower()
                if file_ext not in SUPPORTED_IMAGE_FORMATS:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Unsupported file format: {file_ext}. Supported formats: {', '.join(SUPPORTED_IMAGE_FORMATS.keys())}"
                    )
                
                # Read file content
                file_content = await file.read()
                if len(file_content) == 0:
                    raise HTTPException(status_code=400, detail=f"File {file.filename} is empty")
                
                # Convert to base64 for A2A transmission
                base64_data = base64.b64encode(file_content).decode('utf-8')
                
                # Create processed file info
                processed_file = {
                    "id": str(uuid.uuid4()),
                    "name": Path(file.filename).stem,
                    "original_filename": file.filename,
                    "data": base64_data,
                    "mime_type": SUPPORTED_IMAGE_FORMATS[file_ext],
                    "size": len(file_content),
                    "description": f"Image {i+1} for processing"
                }
                
                processed_files.append(processed_file)
                logger.debug(f"Processed file {i+1}: {file.filename} ({len(file_content)} bytes)")
            
            logger.info(f"Successfully processed {len(processed_files)} files")

            # Forward to Image Processing Agent using A2A SDK
            processing_result = await self.forward_to_image_agent_a2a(
                files=processed_files,
                context_id=context_id,
                description=description
            )
            
            # Return response with A2A metadata
            response = ImageProcessingResponse(
                task_id=task_id,
                context_id=context_id,
                user_id=user_id,
                timestamp=timestamp,
                uploaded_files=len(processed_files),
                files_info=[{
                    "id": f["id"],
                    "name": f["name"], 
                    "original_filename": f["original_filename"],
                    "size": f["size"],
                    "mime_type": f["mime_type"]
                } for f in processed_files],
                processing_result=processing_result,
                status="completed"
            )
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing file upload: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    
    async def forward_to_image_agent_a2a(
        self,
        files: List[Dict],
        context_id: str,
        description: str
    ) -> Dict[str, Any]:
        """Forward the processed files to the Image Processing Agent using A2A SDK."""
        try:
            if not self.a2a_client:
                raise ValueError("A2A client not connected. Call connect_to_agent() first.")

            # Prepare image data for the agent (following the pattern from OCR client)
            image_data = {
                "images": files,
                "message": description
            }

            # Create the message content as JSON string (as expected by the Image Processing Agent)
            message_content = json.dumps(image_data)

            logger.info(f"Sending {len(files)} images for processing via A2A SDK")
            logger.debug(f"Message content preview: {message_content[:200]}...")

            # Create the A2A message using the helper function
            message = create_text_message_object(content=message_content)
            if context_id:
                message.contextId = context_id

            # Create message send parameters
            params = MessageSendParams(
                message=message,
                configuration=MessageSendConfiguration(
                    acceptedOutputModes=["text", "application/json"],
                    blocking=True  # Wait for complete response
                )
            )

            # Create and send the request
            request = SendMessageRequest(
                id=str(uuid4()),
                params=params
            )

            logger.info("Sending A2A request to Image Processing Agent...")
            response: SendMessageResponse = await self.a2a_client.send_message(request)

            # Process the A2A response
            return self._process_a2a_response(response)

        except Exception as e:
            logger.error(f"Error forwarding to Image Processing Agent via A2A: {e}", exc_info=True)
            return {
                "error": f"Failed to communicate with Image Processing Agent: {str(e)}",
                "agent_url": self.agent_url,
                "error_type": type(e).__name__
            }

    def _process_a2a_response(self, response: SendMessageResponse) -> Dict[str, Any]:
        """Process the A2A response from the Image Processing Agent."""
        try:
            if isinstance(response.root, SendMessageSuccessResponse):
                # Success response
                result = response.root.result

                # Try to extract text content from the response
                response_text = None

                if hasattr(result, 'parts') and result.parts:
                    # Extract text from message parts
                    for part in result.parts:
                        if hasattr(part, 'root') and hasattr(part.root, 'text'):
                            response_text = part.root.text
                            break
                elif hasattr(result, 'text'):
                    # Direct text content
                    response_text = result.text

                # If we found text content, try to parse it as JSON
                if response_text:
                    try:
                        parsed_json = json.loads(response_text)
                        return {
                            'status': 'success',
                            'response_type': 'json',
                            'content': parsed_json,
                            'raw_response': response_text
                        }
                    except json.JSONDecodeError:
                        # If not JSON, return as plain text
                        return {
                            'status': 'success',
                            'response_type': 'text',
                            'content': response_text
                        }

                # Fallback: try to extract any available content
                if hasattr(result, 'model_dump'):
                    result_dict = result.model_dump()
                    return {
                        'status': 'success',
                        'response_type': 'structured',
                        'content': result_dict
                    }
                else:
                    return {
                        'status': 'success',
                        'response_type': 'raw',
                        'content': str(result)
                    }
            else:
                # Error response
                return {
                    'status': 'error',
                    'error': str(response.root),
                    'error_details': response.root.model_dump() if hasattr(response.root, 'model_dump') else None
                }

        except Exception as e:
            logger.error(f"Error processing A2A response: {e}", exc_info=True)
            return {
                'status': 'error',
                'error': f"Failed to process A2A response: {str(e)}",
                'error_type': type(e).__name__
            }

    async def startup_event(self):
        """FastAPI startup event to initialize A2A client."""
        try:
            await self.connect_to_agent()
            logger.info("A2A client connected successfully during startup")
        except Exception as e:
            logger.warning(f"Failed to connect A2A client during startup: {e}")
            logger.info("A2A client will attempt to connect on first request")

    async def shutdown_event(self):
        """FastAPI shutdown event to cleanup A2A client."""
        await self.disconnect_from_agent()
        logger.info("A2A client disconnected during shutdown")

    def run(self, host: str = "localhost", port: int = None):
        """Run the FastAPI server with A2A client lifecycle management."""
        if port is None:
            port = self.client_port

        # Add startup and shutdown events
        self.app.add_event_handler("startup", self.startup_event)
        self.app.add_event_handler("shutdown", self.shutdown_event)

        logger.info(f"Starting A2A Image Processing Client on {host}:{port}")
        logger.info(f"Image Processing Agent URL: {self.agent_url}")
        logger.info(f"API Documentation: http://{host}:{port}/docs")
        logger.info("Using A2A SDK for agent communication")

        uvicorn.run(self.app, host=host, port=port)


def create_app(agent_url: str = "http://localhost:8005") -> FastAPI:
    """Create and configure the FastAPI application with A2A client."""
    client = A2AImageProcessingClient(agent_url=agent_url)

    # Add startup and shutdown events for A2A client lifecycle
    client.app.add_event_handler("startup", client.startup_event)
    client.app.add_event_handler("shutdown", client.shutdown_event)

    return client.app


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="A2A Image Processing Client using A2A SDK")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8006, help="Port to bind to")
    parser.add_argument("--agent-url", default="http://localhost:8005", help="Image Processing Agent URL")

    args = parser.parse_args()

    client = A2AImageProcessingClient(agent_url=args.agent_url, client_port=args.port)
    client.run(host=args.host, port=args.port)
