"""
Cursor Tracking Service for Video Frame Analysis
Uses Google ADK memory service and template matching for mouse cursor tracking.
"""

import json
import logging
import numpy as np
import cv2
import base64
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from google.adk.memory import MemoryService

logger = logging.getLogger(__name__)


@dataclass
class CursorPosition:
    """Represents a cursor position with metadata."""
    x: int
    y: int
    timestamp: float
    frame_number: int
    cursor_type: str
    confidence: float
    template_match_score: float = 0.0


@dataclass
class CursorTemplate:
    """Represents a cursor template for matching."""
    name: str
    image_data: np.ndarray
    width: int
    height: int
    cursor_type: str


class CursorTrackingService:
    """
    Service for tracking mouse cursor movements across video frames.
    Uses template matching and ADK memory for persistence.
    """
    
    def __init__(self, memory_service: MemoryService, session_id: str):
        self.memory_service = memory_service
        self.session_id = session_id
        self.cursor_templates: List[CursorTemplate] = []
        self.cursor_history: List[CursorPosition] = []
        
        # Initialize default cursor templates
        self._initialize_cursor_templates()
        
        # Memory keys for persistence
        self.CURSOR_HISTORY_KEY = f"cursor_history_{session_id}"
        self.CURSOR_TEMPLATES_KEY = f"cursor_templates_{session_id}"
        
    def _initialize_cursor_templates(self):
        """Initialize common cursor templates for template matching."""
        # Create basic cursor templates (you can expand this with actual cursor images)
        cursor_types = [
            ("arrow", "default arrow cursor"),
            ("hand", "pointing hand cursor"),
            ("text", "text input cursor"),
            ("loading", "loading/wait cursor"),
            ("pointer", "link pointer cursor")
        ]
        
        for cursor_type, description in cursor_types:
            # In a real implementation, you would load actual cursor image templates
            # For now, we'll create placeholder templates
            template_data = self._create_cursor_template(cursor_type)
            if template_data is not None:
                self.cursor_templates.append(template_data)
    
    def _create_cursor_template(self, cursor_type: str) -> Optional[CursorTemplate]:
        """Create a cursor template for the given type."""
        # This is a placeholder - in practice, you'd load actual cursor images
        # For demonstration, we'll create simple geometric shapes
        
        if cursor_type == "arrow":
            # Create a simple arrow-like template
            template = np.zeros((20, 15, 3), dtype=np.uint8)
            # Draw a simple arrow shape (white on black)
            cv2.fillPoly(template, [np.array([[0,0], [10,8], [6,8], [6,15], [0,15]])], (255,255,255))
            
        elif cursor_type == "hand":
            # Create a hand-like template
            template = np.zeros((20, 20, 3), dtype=np.uint8)
            cv2.circle(template, (10, 10), 8, (255,255,255), -1)
            
        elif cursor_type == "text":
            # Create an I-beam cursor template
            template = np.zeros((20, 8, 3), dtype=np.uint8)
            cv2.line(template, (4, 2), (4, 18), (255,255,255), 1)
            cv2.line(template, (2, 2), (6, 2), (255,255,255), 1)
            cv2.line(template, (2, 18), (6, 18), (255,255,255), 1)
            
        else:
            return None
            
        return CursorTemplate(
            name=cursor_type,
            image_data=template,
            width=template.shape[1],
            height=template.shape[0],
            cursor_type=cursor_type
        )
    
    async def detect_cursor_in_frame(self, frame_data: str, frame_number: int, timestamp: float) -> Optional[CursorPosition]:
        """
        Detect cursor position in a video frame using template matching.
        
        Args:
            frame_data: Base64 encoded image data
            frame_number: Frame number in the video
            timestamp: Timestamp of the frame
            
        Returns:
            CursorPosition if cursor detected, None otherwise
        """
        try:
            # Decode base64 image
            image_bytes = base64.b64decode(frame_data)
            nparr = np.frombuffer(image_bytes, np.uint8)
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if frame is None:
                logger.warning(f"Could not decode frame {frame_number}")
                return None
            
            best_match = None
            best_score = 0.0
            best_location = None
            best_template = None
            
            # Try template matching with each cursor template
            for template in self.cursor_templates:
                result = cv2.matchTemplate(frame, template.image_data, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                
                # Use a threshold to determine if it's a good match
                if max_val > 0.7 and max_val > best_score:  # Adjust threshold as needed
                    best_score = max_val
                    best_location = max_loc
                    best_template = template
            
            if best_match is not None and best_location is not None:
                # Calculate center of cursor
                cursor_x = best_location[0] + best_template.width // 2
                cursor_y = best_location[1] + best_template.height // 2
                
                cursor_pos = CursorPosition(
                    x=cursor_x,
                    y=cursor_y,
                    timestamp=timestamp,
                    frame_number=frame_number,
                    cursor_type=best_template.cursor_type,
                    confidence=best_score,
                    template_match_score=best_score
                )
                
                # Store in memory and history
                await self._store_cursor_position(cursor_pos)
                
                return cursor_pos
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting cursor in frame {frame_number}: {e}")
            return None
    
    async def _store_cursor_position(self, cursor_pos: CursorPosition):
        """Store cursor position in ADK memory service."""
        try:
            # Add to local history
            self.cursor_history.append(cursor_pos)
            
            # Store in ADK memory
            cursor_data = {
                "x": cursor_pos.x,
                "y": cursor_pos.y,
                "timestamp": cursor_pos.timestamp,
                "frame_number": cursor_pos.frame_number,
                "cursor_type": cursor_pos.cursor_type,
                "confidence": cursor_pos.confidence,
                "template_match_score": cursor_pos.template_match_score
            }
            
            # Get existing history from memory
            existing_history = await self.memory_service.get(self.CURSOR_HISTORY_KEY)
            if existing_history:
                history_list = json.loads(existing_history)
            else:
                history_list = []
            
            history_list.append(cursor_data)
            
            # Store updated history
            await self.memory_service.set(
                self.CURSOR_HISTORY_KEY, 
                json.dumps(history_list)
            )
            
            logger.info(f"Stored cursor position: ({cursor_pos.x}, {cursor_pos.y}) at frame {cursor_pos.frame_number}")
            
        except Exception as e:
            logger.error(f"Error storing cursor position: {e}")
    
    async def get_cursor_movement_analysis(self) -> Dict[str, Any]:
        """
        Analyze cursor movement patterns from stored history.
        
        Returns:
            Dictionary containing movement analysis
        """
        try:
            if len(self.cursor_history) < 2:
                return {"status": "insufficient_data", "message": "Need at least 2 cursor positions for analysis"}
            
            # Calculate movement metrics
            movements = []
            total_distance = 0.0
            
            for i in range(1, len(self.cursor_history)):
                prev_pos = self.cursor_history[i-1]
                curr_pos = self.cursor_history[i]
                
                # Calculate distance
                dx = curr_pos.x - prev_pos.x
                dy = curr_pos.y - prev_pos.y
                distance = np.sqrt(dx*dx + dy*dy)
                
                # Calculate time difference
                time_diff = curr_pos.timestamp - prev_pos.timestamp
                
                movement = {
                    "from_frame": prev_pos.frame_number,
                    "to_frame": curr_pos.frame_number,
                    "from_position": {"x": prev_pos.x, "y": prev_pos.y},
                    "to_position": {"x": curr_pos.x, "y": curr_pos.y},
                    "distance": distance,
                    "time_diff": time_diff,
                    "velocity": distance / time_diff if time_diff > 0 else 0,
                    "direction": {"dx": dx, "dy": dy}
                }
                
                movements.append(movement)
                total_distance += distance
            
            # Calculate summary statistics
            velocities = [m["velocity"] for m in movements if m["velocity"] > 0]
            avg_velocity = np.mean(velocities) if velocities else 0
            max_velocity = np.max(velocities) if velocities else 0
            
            analysis = {
                "status": "success",
                "total_positions": len(self.cursor_history),
                "total_distance": total_distance,
                "average_velocity": avg_velocity,
                "max_velocity": max_velocity,
                "movements": movements,
                "cursor_types_detected": list(set(pos.cursor_type for pos in self.cursor_history)),
                "time_span": {
                    "start": self.cursor_history[0].timestamp,
                    "end": self.cursor_history[-1].timestamp,
                    "duration": self.cursor_history[-1].timestamp - self.cursor_history[0].timestamp
                }
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing cursor movement: {e}")
            return {"status": "error", "message": str(e)}
    
    async def clear_cursor_history(self):
        """Clear cursor tracking history from memory."""
        try:
            self.cursor_history.clear()
            await self.memory_service.delete(self.CURSOR_HISTORY_KEY)
            logger.info("Cleared cursor tracking history")
        except Exception as e:
            logger.error(f"Error clearing cursor history: {e}")
    
    async def get_cursor_positions_for_frames(self, frame_numbers: List[int]) -> List[Optional[CursorPosition]]:
        """Get cursor positions for specific frame numbers."""
        result = []
        for frame_num in frame_numbers:
            pos = next((pos for pos in self.cursor_history if pos.frame_number == frame_num), None)
            result.append(pos)
        return result
