"""
Image Processing Agent - A2A compliant agent for image analysis using Google Gemini vision.

This package provides:
- Image analysis using Google Gemini 2.0 Flash model
- A2A protocol compliance for agent-to-agent communication
- Multipart/form-data file upload support
- Structured JSON responses with comprehensive image analysis
- OCR capabilities and visual content understanding
"""

from .agent import create_image_processing_agent, root_agent
from .agent_executor import ImageProcessingExecutor

__version__ = "1.0.0"
__author__ = "Image Processing Agent Team"
__description__ = "A2A compliant image processing agent using Google Gemini vision"

__all__ = [
    "create_image_processing_agent",
    "root_agent", 
    "ImageProcessingExecutor"
]
