#!/usr/bin/env python3
"""
Test script for the Image Processing Agent system.
Validates that both the agent and client are working correctly.
"""

import asyncio
import base64
import io
import json
import logging
import requests
import time
from PIL import Image

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration
AGENT_URL = "http://localhost:8005"
CLIENT_URL = "http://localhost:8006"


def create_test_image() -> bytes:
    """Create a simple test image for testing."""
    # Create a simple test image with text
    img = Image.new('RGB', (400, 200), color='white')
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    return img_bytes.getvalue()


def test_agent_health():
    """Test if the Image Processing Agent is running."""
    try:
        response = requests.get(f"{AGENT_URL}/.well-known/agent.json", timeout=5)
        if response.status_code == 200:
            agent_card = response.json()
            logger.info(f"✅ Agent is running: {agent_card.get('name', 'Unknown')}")
            return True
        else:
            logger.error(f"❌ Agent health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Cannot reach agent: {e}")
        return False


def test_client_health():
    """Test if the A2A Client is running."""
    try:
        response = requests.get(f"{CLIENT_URL}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            logger.info(f"✅ Client is running: {health_data.get('service', 'Unknown')}")
            return True
        else:
            logger.error(f"❌ Client health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Cannot reach client: {e}")
        return False


def test_file_upload():
    """Test file upload through the client."""
    try:
        # Create test image
        test_image_data = create_test_image()
        
        # Prepare files for upload
        files = {
            'files': ('test_image.png', test_image_data, 'image/png')
        }
        
        data = {
            'user_id': 'test_user',
            'description': 'System test image'
        }
        
        logger.info("🧪 Testing file upload...")
        
        # Send request
        response = requests.post(
            f"{CLIENT_URL}/upload",
            files=files,
            data=data,
            timeout=60  # Allow time for processing
        )
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ File upload test successful!")
            logger.info(f"   Task ID: {result.get('task_id')}")
            logger.info(f"   Uploaded files: {result.get('uploaded_files')}")
            
            # Check if we got processing results
            processing_result = result.get('processing_result', {})
            if 'result' in processing_result:
                analysis_result = processing_result['result']
                if analysis_result.get('status') == 'success':
                    logger.info("✅ Image processing successful!")
                    processed_images = analysis_result.get('processed_images', 0)
                    logger.info(f"   Processed images: {processed_images}")
                else:
                    logger.warning("⚠️  Image processing completed but with issues")
            else:
                logger.warning("⚠️  No processing result in response")
            
            return True
        else:
            logger.error(f"❌ File upload failed: {response.status_code}")
            logger.error(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ File upload test failed: {e}")
        return False


def test_direct_a2a():
    """Test direct A2A protocol communication."""
    try:
        # Create test image
        test_image_data = create_test_image()
        base64_data = base64.b64encode(test_image_data).decode('utf-8')
        
        # Prepare A2A message
        image_data = {
            "images": [
                {
                    "id": "test-img-001",
                    "name": "test_image",
                    "original_filename": "test_image.png",
                    "data": base64_data,
                    "mime_type": "image/png",
                    "description": "Direct A2A test image"
                }
            ],
            "message": "Please analyze this test image"
        }
        
        a2a_payload = {
            "jsonrpc": "2.0",
            "method": "message/send",
            "params": {
                "task_id": f"test-{int(time.time())}",
                "context_id": f"ctx-{int(time.time())}",
                "user_id": "direct_test_user",
                "timestamp": int(time.time() * 1000),
                "message": {
                    "messageId": f"msg-{int(time.time())}",
                    "role": "user",
                    "parts": [
                        {
                            "text": json.dumps(image_data)
                        }
                    ],
                    "metadata": {
                        "source": "system_test",
                        "description": "Direct A2A protocol test"
                    }
                }
            },
            "id": f"req-{int(time.time())}"
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        logger.info("🧪 Testing direct A2A protocol...")
        
        response = requests.post(
            f"{AGENT_URL}/a2a/image_processing_agent",
            json=a2a_payload,
            headers=headers,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ Direct A2A test successful!")
            logger.info(f"   Response ID: {result.get('id')}")
            
            # Check result
            if 'result' in result:
                logger.info("✅ A2A processing successful!")
            else:
                logger.warning("⚠️  A2A processing completed but no result")
            
            return True
        else:
            logger.error(f"❌ Direct A2A test failed: {response.status_code}")
            logger.error(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Direct A2A test failed: {e}")
        return False


def main():
    """Run all system tests."""
    print("Image Processing Agent System Test")
    print("=" * 40)
    print()
    
    # Test results
    results = {}
    
    # Test agent health
    print("🔍 Testing Image Processing Agent...")
    results['agent_health'] = test_agent_health()
    print()
    
    # Test client health
    print("🔍 Testing A2A Client...")
    results['client_health'] = test_client_health()
    print()
    
    # Test file upload (if client is running)
    if results['client_health']:
        print("🔍 Testing File Upload...")
        results['file_upload'] = test_file_upload()
        print()
    else:
        results['file_upload'] = False
        logger.warning("⏭️  Skipping file upload test (client not running)")
        print()
    
    # Test direct A2A (if agent is running)
    if results['agent_health']:
        print("🔍 Testing Direct A2A Protocol...")
        results['direct_a2a'] = test_direct_a2a()
        print()
    else:
        results['direct_a2a'] = False
        logger.warning("⏭️  Skipping direct A2A test (agent not running)")
        print()
    
    # Summary
    print("📊 Test Results Summary")
    print("-" * 25)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print()
    print(f"Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! System is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
        print()
        print("💡 Troubleshooting tips:")
        if not results['agent_health']:
            print("   • Start the agent: python start_image_processing_server.py")
        if not results['client_health']:
            print("   • Start the client: python start_image_client.py")
        print("   • Check that ports 8005 and 8006 are available")
        print("   • Verify Google API key is configured in .env file")
        return 1


if __name__ == "__main__":
    exit(main())
